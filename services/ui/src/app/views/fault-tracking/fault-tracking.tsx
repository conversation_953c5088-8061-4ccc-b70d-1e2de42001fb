import { useTranslation } from "react-i18next";
import { ViewBar } from "../../components/view-bar/view-bar";
import { FaultTrackingTable } from "./components/fault-tracking-table";
import { useFaultTrackingData } from "./hooks/use-fault-tracking-data";
import { useFaultTrackingTable } from "./hooks/use-fault-tracking-table";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";
import { useManualEntryModal } from "./hooks/manual-entry/use-manual-entry-modal";
import { ManualEntryModal, ManualEntryButton } from "./components/manual-entry";

export default function FaultTrackingView() {
  const { t } = useTranslation();
  const { isManualEntryModalOpen, toggleManualEntryModal } =
    useManualEntryModal();

  const faultData = useFaultTrackingData();
  const tableConfig = useFaultTrackingTable(faultData.data?.data);

  return (
    <div style={{ flex: 1, width: "100%" }}>
      <ViewBar title={t("faultTracking.title", "Fault Tracking")} />
      <FullPageContainer>
        <FaultTrackingTable
          tableKey={tableConfig.tableKey}
          columns={tableConfig.columns}
          data={tableConfig.tableData}
          rowCount={faultData.data?.metadata.totalResults ?? 0}
          isLoading={faultData.isLoading}
          isFetching={faultData.isFetching}
          error={faultData.error}
          pagination={faultData.pagination}
          setPagination={faultData.setPagination}
          sorting={faultData.sorting}
          setSorting={faultData.setSorting}
          setColumnFilters={faultData.setColumnFilters}
          setGlobalFilter={faultData.setGlobalFilter}
          rowSelection={tableConfig.rowSelection}
          onRowSelectionChange={tableConfig.setRowSelection}
          onRefreshClick={() => {
            tableConfig.handleRefresh();
            faultData.refetch();
          }}
          actionBarItems={
            <ManualEntryButton toggleManualEntryModal={toggleManualEntryModal} />
          }
        />
      </FullPageContainer>
      <ManualEntryModal
        isManualEntryModalOpen={isManualEntryModalOpen}
        toggleManualEntryModal={toggleManualEntryModal}
        onSuccess={() => {
          tableConfig.handleRefresh();
          faultData.refetch();
        }}
      />
    </div>
  );
}
