import { describe, it, expect } from "vitest";
import { formatEditTimingsForAPI } from "./edit-timings-api-formatter";
import type { EditTimingsForm } from "../hooks/edit-timings/use-edit-timings-form";

const createBaseForm = (): EditTimingsForm => ({
  startDate: new Date("2024-02-15"),
  endDate: new Date("2024-02-15"),
  startTime: "10:30",
  startPeriod: "AM" as const,
  endTime: "11:45",
  endPeriod: "AM" as const,
  startSeconds: "15:500",
  endSeconds: "30:250",
  comments: "Test comments",
});

describe("formatEditTimingsForAPI", () => {
  describe("successful formatting", () => {
    it("should format complete form data with start and end times", () => {
      const formData = createBaseForm();

      const result = formatEditTimingsForAPI(formData, "America/New_York");

      expect(result).toEqual({
        startDateLocal: expect.stringMatching(
          /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
        ),
        endDateLocal: expect.stringMatching(
          /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
        ),
        comments: "Test comments",
      });

      // Verify the ISO strings are valid
      expect(new Date(result.startDateLocal!)).toBeInstanceOf(Date);
      expect(new Date(result.endDateLocal!)).toBeInstanceOf(Date);
    });

    it("should handle PM times correctly", () => {
      const formData = createBaseForm();
      formData.startTime = "02:30";
      formData.startPeriod = "PM";
      formData.endTime = "04:45";
      formData.endPeriod = "PM";

      const result = formatEditTimingsForAPI(formData, "America/New_York");

      expect(result.startDateLocal).toBeDefined();
      expect(result.endDateLocal).toBeDefined();
      expect(result.comments).toBe("Test comments");
    });

    it("should handle midnight and noon correctly", () => {
      const formData = createBaseForm();
      formData.startTime = "12:00";
      formData.startPeriod = "AM"; // Midnight
      formData.endTime = "12:00";
      formData.endPeriod = "PM"; // Noon

      const result = formatEditTimingsForAPI(formData, "America/New_York");

      expect(result.startDateLocal).toBeDefined();
      expect(result.endDateLocal).toBeDefined();
    });

    it("should handle empty seconds", () => {
      const formData = createBaseForm();
      formData.startSeconds = "";
      formData.endSeconds = "";

      const result = formatEditTimingsForAPI(formData, "America/New_York");

      expect(result.startDateLocal).toBeDefined();
      expect(result.endDateLocal).toBeDefined();
      expect(result.comments).toBe("Test comments");
    });

    it("should handle invalid seconds format gracefully", () => {
      const formData = createBaseForm();
      formData.startSeconds = "invalid";
      formData.endSeconds = "also-invalid";

      const result = formatEditTimingsForAPI(formData, "America/New_York");

      expect(result.startDateLocal).toBeDefined();
      expect(result.endDateLocal).toBeDefined();
    });

    it("should handle empty comments", () => {
      const formData = createBaseForm();
      formData.comments = "";

      const result = formatEditTimingsForAPI(formData, "America/New_York");

      expect(result.comments).toBe("");
    });

    it("should use default timezone when not specified", () => {
      const formData = createBaseForm();

      const result = formatEditTimingsForAPI(formData); // No timezone specified

      expect(result.startDateLocal).toBeDefined();
      expect(result.endDateLocal).toBeDefined();
      expect(result.comments).toBe("Test comments");
    });

    it("should handle different timezones", () => {
      const formData = createBaseForm();

      const resultEST = formatEditTimingsForAPI(formData, "America/New_York");
      const resultPST = formatEditTimingsForAPI(
        formData,
        "America/Los_Angeles",
      );

      expect(resultEST.startDateLocal).toBeDefined();
      expect(resultPST.startDateLocal).toBeDefined();
      // The times should be different due to timezone conversion
      expect(resultEST.startDateLocal).not.toBe(resultPST.startDateLocal);
    });
  });

  describe("error handling", () => {
    it("should throw error when start date is missing", () => {
      const formData = createBaseForm();
      formData.startDate = null;

      expect(() => formatEditTimingsForAPI(formData)).toThrow(
        "Start date and end date are required",
      );
    });

    it("should throw error when end date is missing", () => {
      const formData = createBaseForm();
      formData.endDate = null;

      expect(() => formatEditTimingsForAPI(formData)).toThrow(
        "Start date and end date are required",
      );
    });

    it("should throw error when both dates are missing", () => {
      const formData = createBaseForm();
      formData.startDate = null;
      formData.endDate = null;

      expect(() => formatEditTimingsForAPI(formData)).toThrow(
        "Start date and end date are required",
      );
    });
  });

  describe("data structure validation", () => {
    it("should only include expected fields in output", () => {
      const formData = createBaseForm();

      const result = formatEditTimingsForAPI(formData);

      // Should only have these 3 fields
      expect(Object.keys(result)).toEqual([
        "startDateLocal",
        "endDateLocal",
        "comments",
      ]);

      // Should NOT include these fields from manual entry
      expect(result).not.toHaveProperty("isIncluded");
      expect(result).not.toHaveProperty("description");
      expect(result).not.toHaveProperty("equipment");
      expect(result).not.toHaveProperty("section");
    });

    it("should have ISO string format for date fields", () => {
      const formData = createBaseForm();

      const result = formatEditTimingsForAPI(formData);

      // Verify ISO 8601 format with timezone (ends with Z for UTC)
      expect(result.startDateLocal).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
      );
      expect(result.endDateLocal).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
      );

      // Verify they can be parsed back to Date objects
      expect(new Date(result.startDateLocal!).getTime()).not.toBeNaN();
      expect(new Date(result.endDateLocal!).getTime()).not.toBeNaN();
    });
  });
});
