import {
  validateSecondsFormat,
  parseSecondsToMilliseconds,
  parseTimeToMinutes,
  getMinStartDate,
  stripTime,
} from "./time-validation";

describe("time-validation", () => {
  describe("validateSecondsFormat", () => {
    it("should return true for empty string (optional field)", () => {
      expect(validateSecondsFormat("")).toBe(true);
    });

    it("should return true for valid seconds:milliseconds format", () => {
      expect(validateSecondsFormat("30:500")).toBe(true);
      expect(validateSecondsFormat("0:0")).toBe(true);
      expect(validateSecondsFormat("59:999")).toBe(true);
      expect(validateSecondsFormat("15:250")).toBe(true);
    });

    it("should return false for invalid format (not two parts)", () => {
      expect(validateSecondsFormat("30")).toBe(false);
      expect(validateSecondsFormat("30:500:100")).toBe(false);
      expect(validateSecondsFormat("30-500")).toBe(false);
    });

    it("should return false for seconds out of range", () => {
      expect(validateSecondsFormat("60:500")).toBe(false);
      expect(validateSecondsFormat("-1:500")).toBe(false);
      expect(validateSecondsFormat("100:500")).toBe(false);
    });

    it("should return false for milliseconds out of range", () => {
      expect(validateSecondsFormat("30:1000")).toBe(false);
      expect(validateSecondsFormat("30:-1")).toBe(false);
      expect(validateSecondsFormat("30:2000")).toBe(false);
    });

    it("should return false for non-numeric values", () => {
      expect(validateSecondsFormat("abc:500")).toBe(false);
      expect(validateSecondsFormat("30:xyz")).toBe(false);
      expect(validateSecondsFormat("abc:xyz")).toBe(false);
    });

    it("should handle edge cases", () => {
      expect(validateSecondsFormat("0:0")).toBe(true);
      expect(validateSecondsFormat("59:999")).toBe(true);
      expect(validateSecondsFormat("00:000")).toBe(true);
    });
  });

  describe("parseSecondsToMilliseconds", () => {
    it("should return null for empty string", () => {
      expect(parseSecondsToMilliseconds("")).toBeNull();
    });

    it("should return null for invalid format", () => {
      expect(parseSecondsToMilliseconds("invalid")).toBeNull();
      expect(parseSecondsToMilliseconds("60:500")).toBeNull();
      expect(parseSecondsToMilliseconds("30:1000")).toBeNull();
    });

    it("should convert valid seconds:milliseconds to total milliseconds", () => {
      expect(parseSecondsToMilliseconds("30:500")).toBe(30500);
      expect(parseSecondsToMilliseconds("0:0")).toBe(0);
      expect(parseSecondsToMilliseconds("1:250")).toBe(1250);
      expect(parseSecondsToMilliseconds("59:999")).toBe(59999);
    });

    it("should handle edge cases", () => {
      expect(parseSecondsToMilliseconds("0:1")).toBe(1);
      expect(parseSecondsToMilliseconds("1:0")).toBe(1000);
    });
  });

  describe("parseTimeToMinutes", () => {
    it("should return null for invalid time format", () => {
      expect(parseTimeToMinutes("invalid", "AM")).toBeNull();
      expect(parseTimeToMinutes("12", "AM")).toBeNull();
      expect(parseTimeToMinutes("abc:def", "AM")).toBeNull();
      // Note: The current implementation doesn't validate minutes range (0-59),
      // so 12:60 would be parsed as valid but incorrect
    });

    it("should convert AM times correctly", () => {
      expect(parseTimeToMinutes("12:00", "AM")).toBe(0); // Midnight
      expect(parseTimeToMinutes("1:00", "AM")).toBe(60);
      expect(parseTimeToMinutes("11:30", "AM")).toBe(690);
      expect(parseTimeToMinutes("6:45", "AM")).toBe(405);
    });

    it("should convert PM times correctly", () => {
      expect(parseTimeToMinutes("12:00", "PM")).toBe(720); // Noon
      expect(parseTimeToMinutes("1:00", "PM")).toBe(780);
      expect(parseTimeToMinutes("11:30", "PM")).toBe(1410);
      expect(parseTimeToMinutes("6:45", "PM")).toBe(1125);
    });

    it("should handle edge cases", () => {
      expect(parseTimeToMinutes("12:00", "AM")).toBe(0); // Midnight
      expect(parseTimeToMinutes("12:00", "PM")).toBe(720); // Noon
      expect(parseTimeToMinutes("11:59", "PM")).toBe(1439); // 23:59
      expect(parseTimeToMinutes("0:00", "AM")).toBe(0); // Should handle leading zero
    });

    it("should handle single digit hours", () => {
      expect(parseTimeToMinutes("9:00", "AM")).toBe(540);
      expect(parseTimeToMinutes("9:00", "PM")).toBe(1260);
    });
  });

  describe("getMinStartDate", () => {
    it("should return a date 30 days before today", () => {
      const today = new Date();
      const minDate = getMinStartDate();

      const expectedDate = new Date(today);
      expectedDate.setDate(expectedDate.getDate() - 30);

      // Compare dates by converting to date strings (ignoring time)
      expect(minDate.toDateString()).toBe(expectedDate.toDateString());
    });

    it("should return a valid date", () => {
      const minDate = getMinStartDate();
      expect(minDate).toBeInstanceOf(Date);
      expect(minDate.getTime()).not.toBeNaN();
    });

    it("should return a date in the past", () => {
      const today = new Date();
      const minDate = getMinStartDate();
      expect(minDate.getTime()).toBeLessThan(today.getTime());
    });
  });

  describe("stripTime", () => {
    it("should remove time portion and keep only date", () => {
      const dateWithTime = new Date("2024-01-15T14:30:45.123Z");
      const strippedDate = stripTime(dateWithTime);

      expect(strippedDate.getFullYear()).toBe(2024);
      expect(strippedDate.getMonth()).toBe(0); // January is 0
      expect(strippedDate.getDate()).toBe(15);
      expect(strippedDate.getHours()).toBe(0);
      expect(strippedDate.getMinutes()).toBe(0);
      expect(strippedDate.getSeconds()).toBe(0);
      expect(strippedDate.getMilliseconds()).toBe(0);
    });

    it("should handle different date inputs", () => {
      const date1 = new Date("2023-12-25T23:59:59.999Z");
      const stripped1 = stripTime(date1);

      expect(stripped1.getFullYear()).toBe(2023);
      expect(stripped1.getMonth()).toBe(11); // December is 11
      expect(stripped1.getDate()).toBe(25);
      expect(stripped1.getHours()).toBe(0);
    });

    it("should handle edge case dates", () => {
      // Use local date instead of UTC to avoid timezone conversion issues
      const newYear = new Date(2024, 0, 1, 12, 30, 45); // Jan 1, 2024, 12:30:45
      const strippedNewYear = stripTime(newYear);

      expect(strippedNewYear.getFullYear()).toBe(2024);
      expect(strippedNewYear.getMonth()).toBe(0);
      expect(strippedNewYear.getDate()).toBe(1);
      expect(strippedNewYear.getHours()).toBe(0);
    });

    it("should return a new Date object (not modify original)", () => {
      const original = new Date("2024-01-15T14:30:45.123Z");
      const originalTime = original.getTime();
      const stripped = stripTime(original);

      // Original should be unchanged
      expect(original.getTime()).toBe(originalTime);
      // Stripped should be different
      expect(stripped.getTime()).not.toBe(originalTime);
      expect(stripped).not.toBe(original);
    });
  });
});
