import type { EditTimingsForm } from "../hooks/edit-timings/use-edit-timings-form";
import { combineDateTime } from "./datetime-formatter";

/**
 * Formats edit-timings form data for API submission
 * @param formData - The edit-timings form data
 * @param facilityTimezone - Facility timezone (defaults to America/New_York)
 * @returns Formatted data for API
 */
export function formatEditTimingsForAPI(
  formData: EditTimingsForm,
  facilityTimezone: string = "America/New_York", // Default to EST/EDT, can be configured
) {
  if (!formData.startDate || !formData.endDate) {
    throw new Error("Start date and end date are required");
  }

  const startDateTime = combineDateTime(
    formData.startDate,
    formData.startTime,
    formData.startPeriod,
    formData.startSeconds,
    facilityTimezone,
  );

  const endDateTime = combineDateTime(
    formData.endDate,
    formData.endTime,
    formData.endPeriod,
    formData.endSeconds,
    facilityTimezone,
  );

  return {
    startDateLocal: startDateTime.toISO(),
    endDateLocal: endDateTime.toISO(),
    comments: formData.comments,
  };
}
