import { renderHook, act } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { useCreateManualEntry } from "./use-create-manual-entry";

// Mock dependencies
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (_key: string, defaultValue: string) => defaultValue,
  }),
}));

vi.mock("../../../../components/toast/use-notification", () => ({
  useNotification: () => ({
    success: vi.fn(),
    error: vi.fn(),
  }),
}));

vi.mock("../../../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn().mockReturnValue({
    setting: { 
      id: "site-time-zone",
      name: "Site Time Zone",
      value: "America/New_York",
      dataType: "string"
    },
    isLoading: false,
    error: null,
  }),
}));

vi.mock("../../../../api/ict-api", () => ({
  ictApi: {
    fetchClient: {
      POST: vi.fn(),
    },
  },
}));

vi.mock("../../utils/manual-entry-api-formatter", () => ({
  formatManualEntryForAPI: vi.fn().mockReturnValue({
    startDateLocal: "2025-09-01T09:00:00.000Z",
    endDateLocal: "2025-09-02T10:00:00.000Z",
    isIncluded: true,
    comments: "test comments",
    description: "test description",
    equipment: "test equipment",
    section: "test section",
  }),
}));

const mockFormData = {
  startDate: new Date("2025-09-01"),
  endDate: new Date("2025-09-02"),
  startTime: "09:00",
  startPeriod: "AM" as const,
  endTime: "10:00",
  endPeriod: "AM" as const,
  startSeconds: "",
  endSeconds: "",
  description: "test description",
  comments: "test comments",
  section: { pk: 1, name: "test section" },
  equipment: { pk: 1, name: "test equipment" },
};

describe("useCreateManualEntry", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should initialize with correct default state", () => {
    const { result } = renderHook(() => useCreateManualEntry());

    expect(result.current.isSubmitting).toBe(false);
    expect(typeof result.current.createEntry).toBe("function");
    expect(result.current.isDisabled).toBe(false);
    expect(result.current.disabledReason).toBeUndefined();
  });

  it("should handle successful submission", async () => {
    const mockPost = vi.fn().mockResolvedValue({
      response: { ok: true, status: 201 },
    });
    
    const { ictApi } = await import("../../../../api/ict-api");
    (ictApi.fetchClient.POST as any) = mockPost;

    const { result } = renderHook(() => useCreateManualEntry());

    let submitResult: boolean;
    await act(async () => {
      submitResult = await result.current.createEntry(mockFormData);
    });

    expect(submitResult!).toBe(true);
    expect(result.current.isSubmitting).toBe(false);
    expect(mockPost).toHaveBeenCalledWith("/availability/alarms", {
      body: expect.any(Object),
      parseAs: "text",
    });
  });

  it("should call onSuccess callback when submission is successful", async () => {
    const mockPost = vi.fn().mockResolvedValue({
      response: { ok: true, status: 201 },
    });
    const mockOnSuccess = vi.fn();
    
    const { ictApi } = await import("../../../../api/ict-api");
    (ictApi.fetchClient.POST as any) = mockPost;

    const { result } = renderHook(() => useCreateManualEntry(mockOnSuccess));

    let submitResult: boolean;
    await act(async () => {
      submitResult = await result.current.createEntry(mockFormData);
    });

    expect(submitResult!).toBe(true);
    expect(mockOnSuccess).toHaveBeenCalledTimes(1);
  });

  it("should not call onSuccess callback when submission fails", async () => {
    const mockPost = vi.fn().mockResolvedValue({
      response: { ok: false, status: 500, statusText: "Internal Server Error" },
    });
    const mockOnSuccess = vi.fn();
    
    const { ictApi } = await import("../../../../api/ict-api");
    (ictApi.fetchClient.POST as any) = mockPost;

    const { result } = renderHook(() => useCreateManualEntry(mockOnSuccess));

    let submitResult: boolean;
    await act(async () => {
      submitResult = await result.current.createEntry(mockFormData);
    });

    expect(submitResult!).toBe(false);
    expect(mockOnSuccess).not.toHaveBeenCalled();
  });

  it("should handle submission failure", async () => {
    const mockPost = vi.fn().mockResolvedValue({
      response: { ok: false, status: 500, statusText: "Internal Server Error" },
    });
    
    const { ictApi } = await import("../../../../api/ict-api");
    (ictApi.fetchClient.POST as any) = mockPost;

    const { result } = renderHook(() => useCreateManualEntry());

    let submitResult: boolean;
    await act(async () => {
      submitResult = await result.current.createEntry(mockFormData);
    });

    expect(submitResult!).toBe(false);
    expect(result.current.isSubmitting).toBe(false);
  });

  it("should handle network errors", async () => {
    const mockPost = vi.fn().mockRejectedValue(new Error("Network error"));
    
    const { ictApi } = await import("../../../../api/ict-api");
    (ictApi.fetchClient.POST as any) = mockPost;

    const { result } = renderHook(() => useCreateManualEntry());

    let submitResult: boolean;
    await act(async () => {
      submitResult = await result.current.createEntry(mockFormData);
    });

    expect(submitResult!).toBe(false);
    expect(result.current.isSubmitting).toBe(false);
  });

  it("should handle JSON parsing errors with valid response", async () => {
    const parseError = new Error("Failed to parse JSON");
    parseError.message = "JSON parse error";
    (parseError as any).response = { ok: true, status: 201 };
    
    const mockPost = vi.fn().mockRejectedValue(parseError);
    
    const { ictApi } = await import("../../../../api/ict-api");
    (ictApi.fetchClient.POST as any) = mockPost;

    const { result } = renderHook(() => useCreateManualEntry());

    let submitResult: boolean;
    await act(async () => {
      submitResult = await result.current.createEntry(mockFormData);
    });

    expect(submitResult!).toBe(true);
    expect(result.current.isSubmitting).toBe(false);
  });

  it("should call onSuccess callback when JSON parsing fails but response is ok", async () => {
    const parseError = new Error("Failed to parse JSON");
    parseError.message = "JSON parse error";
    (parseError as any).response = { ok: true, status: 201 };
    const mockOnSuccess = vi.fn();
    
    const mockPost = vi.fn().mockRejectedValue(parseError);
    
    const { ictApi } = await import("../../../../api/ict-api");
    (ictApi.fetchClient.POST as any) = mockPost;

    const { result } = renderHook(() => useCreateManualEntry(mockOnSuccess));

    let submitResult: boolean;
    await act(async () => {
      submitResult = await result.current.createEntry(mockFormData);
    });

    expect(submitResult!).toBe(true);
    expect(mockOnSuccess).toHaveBeenCalledTimes(1);
  });

  describe("timezone configuration validation", () => {
    it("should be disabled when timezone config is missing", async () => {
      const { useConfigSetting } = await import("../../../../config/hooks/use-config");
      const mockUseConfig = vi.mocked(useConfigSetting);
      mockUseConfig.mockReturnValue({
        setting: undefined,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useCreateManualEntry());

      expect(result.current.isDisabled).toBe(true);
      expect(result.current.disabledReason).toBe("Timezone configuration is required for manual entries");
    });

    it("should be disabled when timezone config is loading", async () => {
      const { useConfigSetting } = await import("../../../../config/hooks/use-config");
      const mockUseConfig = vi.mocked(useConfigSetting);
      mockUseConfig.mockReturnValue({
        setting: undefined,
        isLoading: true,
        error: null,
      });

      const { result } = renderHook(() => useCreateManualEntry());

      expect(result.current.isDisabled).toBe(true);
      expect(result.current.disabledReason).toBe("Loading timezone configuration...");
    });

    it("should be disabled when timezone config value is empty", async () => {
      const { useConfigSetting } = await import("../../../../config/hooks/use-config");
      const mockUseConfig = vi.mocked(useConfigSetting);
      mockUseConfig.mockReturnValue({
        setting: { 
          id: "site-time-zone",
          name: "Site Time Zone",
          value: "",
          dataType: "string"
        },
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useCreateManualEntry());

      expect(result.current.isDisabled).toBe(true);
      expect(result.current.disabledReason).toBe("Timezone configuration is required for manual entries");
    });

    it("should be enabled when timezone config is available", async () => {
      const { useConfigSetting } = await import("../../../../config/hooks/use-config");
      const mockUseConfig = vi.mocked(useConfigSetting);
      mockUseConfig.mockReturnValue({
        setting: { 
          id: "site-time-zone",
          name: "Site Time Zone",
          value: "America/New_York",
          dataType: "string"
        },
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useCreateManualEntry());

      expect(result.current.isDisabled).toBe(false);
      expect(result.current.disabledReason).toBeUndefined();
    });

    it("should prevent submission when timezone config is missing", async () => {
      const { useConfigSetting } = await import("../../../../config/hooks/use-config");
      const mockUseConfig = vi.mocked(useConfigSetting);
      mockUseConfig.mockReturnValue({
        setting: undefined,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useCreateManualEntry());

      let submitResult: boolean;
      await act(async () => {
        submitResult = await result.current.createEntry(mockFormData);
      });

      expect(submitResult!).toBe(false);
      expect(result.current.isSubmitting).toBe(false);
    });

    it("should prevent submission when timezone config is loading", async () => {
      const { useConfigSetting } = await import("../../../../config/hooks/use-config");
      const mockUseConfig = vi.mocked(useConfigSetting);
      mockUseConfig.mockReturnValue({
        setting: undefined,
        isLoading: true,
        error: null,
      });

      const { result } = renderHook(() => useCreateManualEntry());

      let submitResult: boolean;
      await act(async () => {
        submitResult = await result.current.createEntry(mockFormData);
      });

      expect(submitResult!).toBe(false);
      expect(result.current.isSubmitting).toBe(false);
    });
  });
});
