import { renderHook } from "@testing-library/react";
import { useManualEntryValidation } from "./use-manual-entry-validation";
import type { ManualEntryForm } from "./use-manual-entry-form";

// Mock react-i18next with stable t function
const mockT = vi.fn((_key: string, defaultValue: string) => defaultValue);
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

// Mock the utility functions
vi.mock("../utils/manual-entry-validation", () => ({
  validateSecondsFormat: vi.fn((input: string) => {
    const regex = /^\d{1,2}:\d{1,3}$/;
    return regex.test(input);
  }),
  parseSecondsToMilliseconds: vi.fn((input: string) => {
    if (!input) return null;
    const parts = input.split(":");
    if (parts.length !== 2) return null;
    const sec = parseInt(parts[0], 10);
    const ms = parseInt(parts[1], 10);
    if (isNaN(sec) || isNaN(ms)) return null;
    return sec * 1000 + ms;
  }),
  parseTimeToMinutes: vi.fn((time: string, period: "AM" | "PM") => {
    if (!/^\d{1,2}:\d{2}$/.test(time)) return null;
    const [hourStr, minuteStr] = time.split(":");
    let hour = parseInt(hourStr, 10);
    const minutes = parseInt(minuteStr, 10);
    if (isNaN(hour) || isNaN(minutes)) return null;
    if (period === "AM" && hour === 12) hour = 0;
    if (period === "PM" && hour < 12) hour += 12;
    return hour * 60 + minutes;
  }),
  getMinStartDate: vi.fn(() => {
    const date = new Date();
    date.setDate(date.getDate() - 30);
    return date;
  }),
  stripTime: vi.fn((date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate());
  }),
}));

describe("useManualEntryValidation", () => {
  const baseFormData: ManualEntryForm = {
    startDate: null,
    endDate: null,
    startTime: "",
    startPeriod: "AM",
    endTime: "",
    endPeriod: "AM",
    startSeconds: "",
    endSeconds: "",
    description: "",
    comments: "",
    section: null,
    equipment: null,
  };

  it("should return validation functions", () => {
    const { result } = renderHook(() => useManualEntryValidation(baseFormData));

    expect(typeof result.current.validateForm).toBe("function");
    expect(typeof result.current.isValid).toBe("function");
  });

  describe("validateForm", () => {
    it("should return errors for empty required fields", () => {
      const { result } = renderHook(() =>
        useManualEntryValidation(baseFormData),
      );

      const errors = result.current.validateForm();

      expect(errors).toHaveProperty("startDate");
      expect(errors).toHaveProperty("endDate");
      expect(errors).toHaveProperty("startTime");
      expect(errors).toHaveProperty("endTime");
      expect(errors).toHaveProperty("description");
      expect(errors).toHaveProperty("comments");
    });

    it("should return no errors for valid form data", () => {
      const validFormData: ManualEntryForm = {
        ...baseFormData,
        startDate: new Date(),
        endDate: new Date(),
        startTime: "10:00",
        endTime: "11:00",
        description: "Valid description",
        comments: "Valid comments",
        section: { pk: 1, name: "Test Section" },
        equipment: { pk: 1, name: "Test Equipment" },
      };

      // Mock current time to be later than the selected times to avoid future time validation
      const mockNow = new Date();
      mockNow.setHours(15, 0, 0, 0); // Set to 3:00 PM
      vi.setSystemTime(mockNow);

      const { result } = renderHook(() =>
        useManualEntryValidation(validFormData),
      );

      const errors = result.current.validateForm();

      expect(Object.keys(errors)).toHaveLength(0);

      vi.useRealTimers();
    });

    it("should validate seconds format", () => {
      const formDataWithInvalidSeconds: ManualEntryForm = {
        ...baseFormData,
        startDate: new Date(),
        endDate: new Date(),
        startTime: "10:00",
        endTime: "11:00",
        description: "Valid description",
        comments: "Valid comments",
        startSeconds: "invalid",
      };

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithInvalidSeconds),
      );

      const errors = result.current.validateForm();

      expect(errors.startSeconds).toContain("Format should be ss:ms");
    });

    it("should validate date order", () => {
      const formDataWithInvalidDateOrder: ManualEntryForm = {
        ...baseFormData,
        startDate: new Date("2024-01-16"),
        endDate: new Date("2024-01-15"), // End before start
        startTime: "10:00",
        endTime: "11:00",
        description: "Valid description",
        comments: "Valid comments",
      };

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithInvalidDateOrder),
      );

      const errors = result.current.validateForm();

      expect(errors.endDate).toContain("End date cannot be before start date");
    });

    it("should validate time order on same day", () => {
      const sameDate = new Date("2024-01-15");
      const formDataWithInvalidTimeOrder: ManualEntryForm = {
        ...baseFormData,
        startDate: sameDate,
        endDate: sameDate,
        startTime: "11:00",
        endTime: "10:00", // End before start
        description: "Valid description",
        comments: "Valid comments",
      };

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithInvalidTimeOrder),
      );

      const errors = result.current.validateForm();

      expect(errors.endTime).toContain("End time cannot be before start time");
    });

    it("should validate same time requires precision", () => {
      const sameDate = new Date("2024-01-15");
      const formDataWithSameTime: ManualEntryForm = {
        ...baseFormData,
        startDate: sameDate,
        endDate: sameDate,
        startTime: "10:00",
        endTime: "10:00", // Same time
        description: "Valid description",
        comments: "Valid comments",
      };

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithSameTime),
      );

      const errors = result.current.validateForm();

      expect(errors.endTime).toContain("End time must be after start time");
    });

    it("should validate seconds precision when times are same", () => {
      const sameDate = new Date("2024-01-15");
      const formDataWithSameTimeAndSeconds: ManualEntryForm = {
        ...baseFormData,
        startDate: sameDate,
        endDate: sameDate,
        startTime: "10:00",
        endTime: "10:00",
        startSeconds: "30:500",
        endSeconds: "30:400", // End seconds less than start
        description: "Valid description",
        comments: "Valid comments",
      };

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithSameTimeAndSeconds),
      );

      const errors = result.current.validateForm();

      expect(errors.endSeconds).toContain(
        "End seconds:milliseconds must be after start seconds:milliseconds",
      );
    });

    it("should require end seconds when start seconds is provided and times are same", () => {
      const sameDate = new Date("2024-01-15");
      const formDataWithMissingEndSeconds: ManualEntryForm = {
        ...baseFormData,
        startDate: sameDate,
        endDate: sameDate,
        startTime: "10:00",
        endTime: "10:00",
        startSeconds: "30:500",
        endSeconds: "", // Missing end seconds
        description: "Valid description",
        comments: "Valid comments",
      };

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithMissingEndSeconds),
      );

      const errors = result.current.validateForm();

      expect(errors.endSeconds).toContain(
        "End seconds:milliseconds required when start seconds:milliseconds is specified",
      );
    });

    it("should allow end seconds without start seconds when times are same", () => {
      const sameDate = new Date(); // Use current date to avoid min date validation
      const formDataWithOnlyEndSeconds: ManualEntryForm = {
        ...baseFormData,
        startDate: sameDate,
        endDate: sameDate,
        startTime: "10:00",
        endTime: "10:00",
        startSeconds: "",
        endSeconds: "30:500", // End seconds makes it valid
        description: "Valid description",
        section: { pk: 1, name: "Test Section" },
        equipment: { pk: 1, name: "Test Equipment" },
        comments: "Valid comments",
      };

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithOnlyEndSeconds),
      );

      const errors = result.current.validateForm();

      expect(Object.keys(errors)).toHaveLength(0);
    });

    it("should validate trimmed description and comments", () => {
      const formDataWithWhitespace: ManualEntryForm = {
        ...baseFormData,
        startDate: new Date(),
        endDate: new Date(),
        startTime: "10:00",
        endTime: "11:00",
        description: "   ", // Only whitespace
        comments: "\t\n", // Only whitespace
      };

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithWhitespace),
      );

      const errors = result.current.validateForm();

      expect(errors.description).toContain("Description is required");
      expect(errors.comments).toContain("Comments are required");
    });

    it("should validate future start date", () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1); // Tomorrow

      const formDataWithFutureStartDate: ManualEntryForm = {
        ...baseFormData,
        startDate: futureDate,
        endDate: new Date(),
        startTime: "10:00",
        endTime: "11:00",
        description: "Valid description",
        comments: "Valid comments",
        section: { pk: 1, name: "Test Section" },
        equipment: { pk: 1, name: "Test Equipment" },
      };

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithFutureStartDate),
      );

      const errors = result.current.validateForm();

      expect(errors.startDate).toContain("Start date cannot be in the future");
    });

    it("should validate future end date", () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1); // Tomorrow

      const formDataWithFutureEndDate: ManualEntryForm = {
        ...baseFormData,
        startDate: new Date(),
        endDate: futureDate,
        startTime: "10:00",
        endTime: "11:00",
        description: "Valid description",
        comments: "Valid comments",
        section: { pk: 1, name: "Test Section" },
        equipment: { pk: 1, name: "Test Equipment" },
      };

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithFutureEndDate),
      );

      const errors = result.current.validateForm();

      expect(errors.endDate).toContain("End date cannot be in the future");
    });

    it("should validate future start time on today's date", () => {
      const today = new Date();
      const futureTime = "23:59"; // Close to end of day to avoid timing issues

      const formDataWithFutureStartTime: ManualEntryForm = {
        ...baseFormData,
        startDate: today,
        endDate: today,
        startTime: futureTime,
        startPeriod: "PM",
        endTime: "10:00",
        endPeriod: "AM",
        description: "Valid description",
        comments: "Valid comments",
        section: { pk: 1, name: "Test Section" },
        equipment: { pk: 1, name: "Test Equipment" },
      };

      // Mock current time to be earlier than the selected time
      const mockNow = new Date();
      mockNow.setHours(10, 0, 0, 0); // Set to 10:00 AM
      vi.setSystemTime(mockNow);

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithFutureStartTime),
      );

      const errors = result.current.validateForm();

      expect(errors.startTime).toContain("Start time cannot be in the future");

      vi.useRealTimers();
    });

    it("should validate future end time on today's date", () => {
      const today = new Date();
      const futureTime = "23:59"; // Close to end of day to avoid timing issues

      const formDataWithFutureEndTime: ManualEntryForm = {
        ...baseFormData,
        startDate: today,
        endDate: today,
        startTime: "09:00",
        startPeriod: "AM",
        endTime: futureTime,
        endPeriod: "PM",
        description: "Valid description",
        comments: "Valid comments",
        section: { pk: 1, name: "Test Section" },
        equipment: { pk: 1, name: "Test Equipment" },
      };

      // Mock current time to be earlier than the selected time
      const mockNow = new Date();
      mockNow.setHours(10, 0, 0, 0); // Set to 10:00 AM
      vi.setSystemTime(mockNow);

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithFutureEndTime),
      );

      const errors = result.current.validateForm();

      expect(errors.endTime).toContain("End time cannot be in the future");

      vi.useRealTimers();
    });

    it("should allow past times on today's date", () => {
      const today = new Date();

      const formDataWithPastTime: ManualEntryForm = {
        ...baseFormData,
        startDate: today,
        endDate: today,
        startTime: "09:00",
        startPeriod: "AM",
        endTime: "09:30",
        endPeriod: "AM",
        description: "Valid description",
        comments: "Valid comments",
        section: { pk: 1, name: "Test Section" },
        equipment: { pk: 1, name: "Test Equipment" },
      };

      // Mock current time to be later than the selected times
      const mockNow = new Date();
      mockNow.setHours(15, 0, 0, 0); // Set to 3:00 PM
      vi.setSystemTime(mockNow);

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithPastTime),
      );

      const errors = result.current.validateForm();

      expect(errors.startTime).toBeUndefined();
      expect(errors.endTime).toBeUndefined();

      vi.useRealTimers();
    });

    it("should allow any time on past dates", () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      const formDataWithPastDate: ManualEntryForm = {
        ...baseFormData,
        startDate: yesterday,
        endDate: yesterday,
        startTime: "10:00", // Any time should be allowed on past dates
        startPeriod: "AM",
        endTime: "11:00", // End time after start time
        endPeriod: "AM",
        description: "Valid description",
        comments: "Valid comments",
        section: { pk: 1, name: "Test Section" },
        equipment: { pk: 1, name: "Test Equipment" },
      };

      const { result } = renderHook(() =>
        useManualEntryValidation(formDataWithPastDate),
      );

      const errors = result.current.validateForm();

      expect(errors.startTime).toBeUndefined();
      expect(errors.endTime).toBeUndefined();
    });
  });

  describe("isValid", () => {
    it("should return false for invalid form", () => {
      const { result } = renderHook(() =>
        useManualEntryValidation(baseFormData),
      );

      const isValid = result.current.isValid();

      expect(isValid).toBe(false);
    });

    it("should return true for valid form", () => {
      const validFormData: ManualEntryForm = {
        ...baseFormData,
        startDate: new Date(),
        endDate: new Date(),
        startTime: "10:00",
        endTime: "11:00",
        description: "Valid description",
        comments: "Valid comments",
        section: { pk: 1, name: "Test Section" },
        equipment: { pk: 1, name: "Test Equipment" },
      };

      // Mock current time to be later than the selected times to avoid future time validation
      const mockNow = new Date();
      mockNow.setHours(15, 0, 0, 0); // Set to 3:00 PM
      vi.setSystemTime(mockNow);

      const { result } = renderHook(() =>
        useManualEntryValidation(validFormData),
      );

      const isValid = result.current.isValid();

      expect(isValid).toBe(true);

      vi.useRealTimers();
    });

    it("should provide consistent validation interface", () => {
      const { result, rerender } = renderHook(() =>
        useManualEntryValidation(baseFormData),
      );

      const firstValidation = result.current.validateForm;
      const firstIsValid = result.current.isValid;

      // Verify functions exist and are callable
      expect(typeof firstValidation).toBe("function");
      expect(typeof firstIsValid).toBe("function");

      rerender();

      const secondValidation = result.current.validateForm;
      const secondIsValid = result.current.isValid;

      // Functions should still be callable after rerender
      expect(typeof secondValidation).toBe("function");
      expect(typeof secondIsValid).toBe("function");

      // Results should be consistent for same data
      expect(firstValidation()).toEqual(secondValidation());
      expect(firstIsValid()).toBe(secondIsValid());
    });

    it("should update when form data changes", () => {
      const { result, rerender } = renderHook(
        (formData: ManualEntryForm) => useManualEntryValidation(formData),
        { initialProps: baseFormData },
      );

      expect(result.current.isValid()).toBe(false);

      const validFormData: ManualEntryForm = {
        ...baseFormData,
        startDate: new Date(),
        endDate: new Date(),
        startTime: "10:00",
        endTime: "11:00",
        description: "Valid description",
        comments: "Valid comments",
        section: { pk: 1, name: "Test Section" },
        equipment: { pk: 1, name: "Test Equipment" },
      };

      // Mock current time to be later than the selected times to avoid future time validation
      const mockNow = new Date();
      mockNow.setHours(15, 0, 0, 0); // Set to 3:00 PM
      vi.setSystemTime(mockNow);

      rerender(validFormData);

      expect(result.current.isValid()).toBe(true);

      vi.useRealTimers();
    });
  });
});
