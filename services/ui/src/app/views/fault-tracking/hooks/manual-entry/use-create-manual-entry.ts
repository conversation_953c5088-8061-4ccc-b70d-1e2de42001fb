import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNotification } from "../../../../components/toast/use-notification";
import { useConfigSetting } from "../../../../config/hooks/use-config";
import { ictApi } from "../../../../api/ict-api";
import { formatManualEntryForAPI } from "../../utils/manual-entry-api-formatter";
import { Logger } from "../../../../utils/logger";
import type { ManualEntryForm } from "./use-manual-entry-form";

const logger = new Logger("ManualEntryCreation");

export function useCreateManualEntry(onSuccess?: () => void) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { t } = useTranslation();
  const { success, error: showError } = useNotification();
  const { setting: timezoneConfig, isLoading: isTimezoneLoading } = useConfigSetting("site-time-zone");
  
  // Check if timezone config is available
  const isTimezoneConfigured = Boolean(timezoneConfig?.value);
  const isDisabled = !isTimezoneConfigured || isTimezoneLoading;
  
  const disabledReason = isTimezoneLoading 
    ? t("faultTracking.timezoneConfigLoading", "Loading timezone configuration...")
    : !isTimezoneConfigured 
      ? t("faultTracking.timezoneConfigMissing", "Timezone configuration is required for manual entries")
      : undefined;

  const createEntry = async (formData: ManualEntryForm): Promise<boolean> => {
    // Prevent submission if timezone is not configured
    if (isDisabled) {
      logger.error("Manual entry creation blocked - timezone not configured", {
        isTimezoneLoading,
        isTimezoneConfigured,
        disabledReason,
      });
      showError(disabledReason || t("faultTracking.manualEntryUnavailable", "Manual entry is currently unavailable"));
      return false;
    }

    setIsSubmitting(true);

    try {
      const facilityTimezone = timezoneConfig!.value as string;
      const apiPayload = formatManualEntryForAPI(
        formData,
        formData.section?.name || "",
        formData.equipment?.name || "",
        facilityTimezone,
      );

      logger.info("Creating manual alarm entry", {
        section: formData.section?.name,
        equipment: formData.equipment?.name,
        description: formData.description,
        facilityTimezone,
      });

      const fetchResponse = ictApi.fetchClient.POST("/availability/alarms", {
        body: apiPayload,
        parseAs: "text",
      });

      let response: { response?: { ok?: boolean; status?: number; statusText?: string } };
      try {
        response = await fetchResponse;
      } catch (parseError: unknown) {
        if (parseError instanceof Error && parseError.message?.includes('JSON') && 'response' in parseError) {
          const errorWithResponse = parseError as Error & { response?: { ok?: boolean; status?: number; statusText?: string } };
          logger.info("JSON parse error but HTTP response is ok, treating as success", {
            parseError: parseError.message,
            status: errorWithResponse.response?.status,
          });
          response = errorWithResponse;
        } else {
          throw parseError;
        }
      }

      if (response.response?.ok) {
        logger.info("Manual alarm entry created successfully", {
          status: response.response.status,
          section: formData.section?.name,
          equipment: formData.equipment?.name,
        });
        
        success(
          t(
            "faultTracking.manualEntrySavedSuccessfully",
            "Manual entry saved successfully",
          ),
        );
        // Trigger the onSuccess callback to refresh the table
        if (onSuccess) {
          onSuccess();
        }
        return true;
      } else {
        throw new Error(`HTTP ${response.response?.status}: ${response.response?.statusText}`);
      }
    } catch (error: unknown) {
      logger.error("Failed to create manual alarm entry", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          section: formData.section?.name,
          equipment: formData.equipment?.name,
          description: formData.description,
          facilityTimezone: timezoneConfig?.value as string || "unknown",
        },
      });
      
      showError(
        t(
          "faultTracking.manualEntrySubmissionFailed",
          "Failed to save manual entry. Please try again.",
        ),
      );
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    createEntry,
    isSubmitting,
    isDisabled,
    disabledReason,
  };
}
