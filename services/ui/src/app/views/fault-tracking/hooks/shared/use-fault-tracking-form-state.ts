import { useReducer, useState, useCallback } from "react";
import type { Section, Equipment } from "../../types/types";

type FormAction<TForm, <PERSON><PERSON><PERSON> extends keyof TForm> =
  | {
      type: "SET_FIELD";
      field: TField;
      value: string | Date | number | Section | Equipment | null;
    }
  | { type: "RESET" };

/**
 * Creates a reducer for form state management
 * @param initialState - The initial form state
 * @returns Reducer function for form state updates
 */
function createFormReducer<TForm, <PERSON><PERSON><PERSON> extends keyof TForm>(
  initialState: TForm,
) {
  return function reducer(
    state: TForm,
    action: FormAction<TForm, TField>,
  ): TForm {
    switch (action.type) {
      case "SET_FIELD":
        return { ...state, [action.field]: action.value };
      case "RESET":
        return initialState;
      default:
        return state;
    }
  };
}

/**
 * Generic fault tracking form state hook that provides form data management
 * @param initialFormState - The initial state for the form
 * @returns Form state management functionality
 */
export function useFaultTrackingFormState<TForm, <PERSON><PERSON><PERSON> extends keyof TForm>(
  initialFormState: TForm,
) {
  const reducer = createFormReducer<TForm, TField>(initialFormState);
  const [formData, dispatch] = useReducer(reducer, initialFormState);
  const [errors, setErrors] = useState<Partial<Record<TField, string>>>({});
  const [touched, setTouched] = useState<Partial<Record<TField, boolean>>>({});

  const setField = useCallback(
    (
      field: TField,
      value: string | Date | number | Section | Equipment | null,
    ) => {
      dispatch({ type: "SET_FIELD", field, value });
    },
    [],
  );

  const setFieldTouched = useCallback((field: TField) => {
    setTouched((prev) => ({ ...prev, [field]: true }));
  }, []);

  const resetState = useCallback(() => {
    dispatch({ type: "RESET" });
  }, []);

  const resetErrors = useCallback(() => {
    setErrors({});
  }, []);

  const resetTouched = useCallback(() => {
    setTouched({});
  }, []);

  const resetAll = useCallback(() => {
    resetState();
    resetErrors();
    resetTouched();
  }, [resetState, resetErrors, resetTouched]);

  return {
    formData,
    setField,
    resetState,
    resetAll,
    errors,
    setErrors,
    resetErrors,
    touched,
    setFieldTouched,
    resetTouched,
  };
}
