import { useMemo, useState, useEffect } from "react";
import type { FaultAlarm } from "../types/types";
import { transformFaultData } from "../utils/cell-data-transforms";
import { createFaultTrackingColumns } from "../components/fault-tracking-columns";

export function useFaultTrackingTable(apiData?: FaultAlarm[]) {
  const [tableData, setTableData] = useState<FaultAlarm[]>([]);
  const [tableKey, setTableKey] = useState(0);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  // Update tableData when API data changes - transform data here
  useEffect(() => {
    if (apiData) {
      const transformedData = transformFaultData(apiData);
      setTableData(transformedData);
    }
  }, [apiData]);

  const selectedFaults = useMemo(() => {
    return tableData.filter((_row, index) => rowSelection[index]);
  }, [rowSelection, tableData]);

  // Use centralized column definitions
  const columns = useMemo(() => createFaultTrackingColumns(), []);

  const handleRefresh = () => {
    setTableKey((prev) => prev + 1);
  };

  return {
    // Table data
    tableData,
    setTableData,
    tableKey,

    // Selection
    rowSelection,
    setRowSelection,
    selectedFaults,

    // Configuration
    columns,

    // Actions
    handleRefresh,
  };
}
