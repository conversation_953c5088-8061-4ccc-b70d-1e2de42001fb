import type {
  EditTimingsForm,
  EditTimingsFormField,
} from "./use-edit-timings-form";
import { useFaultTrackingFormState } from "../shared/use-fault-tracking-form-state";

const initialFormState: EditTimingsForm = {
  startDate: null,
  endDate: null,
  startTime: "",
  startPeriod: "AM",
  endTime: "",
  endPeriod: "AM",
  startSeconds: "",
  endSeconds: "",
  comments: "",
};

export function useEditTimingsFormState() {
  return useFaultTrackingFormState<EditTimingsForm, EditTimingsFormField>(
    initialFormState,
  );
}
