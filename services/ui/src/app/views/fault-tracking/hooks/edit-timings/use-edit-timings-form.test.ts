import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook } from "@testing-library/react";
import { useEditTimingsForm, getMinStartDate } from "./use-edit-timings-form";

// Mock the form state hook
vi.mock("./use-edit-timings-form-state", () => ({
  useEditTimingsFormState: vi.fn(),
}));

// Mock the validation hook
vi.mock("./use-edit-timings-validation", () => ({
  useEditTimingsValidation: vi.fn(),
}));

// Mock the shared fault tracking form hook
vi.mock("../shared/use-fault-tracking-form", () => ({
  useFaultTrackingForm: vi.fn(),
  getMinStartDate: vi.fn().mockReturnValue(new Date("2024-01-15")),
}));

import { useEditTimingsFormState } from "./use-edit-timings-form-state";
import { useEditTimingsValidation } from "./use-edit-timings-validation";
import { useFaultTrackingForm } from "../shared/use-fault-tracking-form";

describe("useEditTimingsForm", () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mock return value
    vi.mocked(useFaultTrackingForm).mockReturnValue({
      formData: {
        startDate: null,
        endDate: null,
        startTime: "",
        startPeriod: "AM",
        endTime: "",
        endPeriod: "AM",
        startSeconds: "",
        endSeconds: "",
        comments: "",
      },
      setField: vi.fn(),
      reset: vi.fn(),
      validate: vi.fn(),
      setErrors: vi.fn(),
      errors: {},
      touched: {},
      setFieldTouched: vi.fn(),
      isValid: true,
      calculateDuration: vi.fn().mockReturnValue("0h 0m"),
      resetTrigger: 0,
    });
  });

  describe("basic functionality", () => {
    it("should call useFaultTrackingForm with correct hooks", () => {
      renderHook(() => useEditTimingsForm());

      expect(vi.mocked(useFaultTrackingForm)).toHaveBeenCalledWith(
        useEditTimingsFormState,
        useEditTimingsValidation,
      );
    });

    it("should return the result from useFaultTrackingForm", () => {
      const mockResult = {
        formData: {
          startDate: new Date("2024-02-15"),
          endDate: new Date("2024-02-15"),
          startTime: "10:00",
          startPeriod: "AM" as const,
          endTime: "11:00",
          endPeriod: "AM" as const,
          startSeconds: "",
          endSeconds: "",
          comments: "Test",
        },
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: { comments: "Required" },
        touched: { comments: true },
        setFieldTouched: vi.fn(),
        isValid: false,
        calculateDuration: vi.fn().mockReturnValue("1h 0m"),
        resetTrigger: 1,
      };

      vi.mocked(useFaultTrackingForm).mockReturnValue(mockResult);

      const { result } = renderHook(() => useEditTimingsForm());

      expect(result.current).toBe(mockResult);
    });

    it("should provide all expected form functionality", () => {
      const { result } = renderHook(() => useEditTimingsForm());

      // Verify all expected properties and functions are available
      expect(result.current).toHaveProperty("formData");
      expect(result.current).toHaveProperty("setField");
      expect(result.current).toHaveProperty("reset");
      expect(result.current).toHaveProperty("validate");
      expect(result.current).toHaveProperty("setErrors");
      expect(result.current).toHaveProperty("errors");
      expect(result.current).toHaveProperty("touched");
      expect(result.current).toHaveProperty("setFieldTouched");
      expect(result.current).toHaveProperty("isValid");
      expect(result.current).toHaveProperty("calculateDuration");
      expect(result.current).toHaveProperty("resetTrigger");
    });
  });

  describe("hook composition", () => {
    it("should pass the form state hook to useFaultTrackingForm", () => {
      renderHook(() => useEditTimingsForm());

      const [formStateHook] = vi.mocked(useFaultTrackingForm).mock.calls[0];
      expect(formStateHook).toBe(useEditTimingsFormState);
    });

    it("should pass the validation hook to useFaultTrackingForm", () => {
      renderHook(() => useEditTimingsForm());

      const [, validationHook] = vi.mocked(useFaultTrackingForm).mock.calls[0];
      expect(validationHook).toBe(useEditTimingsValidation);
    });

    it("should maintain hook identity across re-renders", () => {
      const { rerender } = renderHook(() => useEditTimingsForm());

      expect(vi.mocked(useFaultTrackingForm)).toHaveBeenCalledTimes(1);

      rerender();

      // useFaultTrackingForm should be called again on re-render
      expect(vi.mocked(useFaultTrackingForm)).toHaveBeenCalledTimes(2);

      // But with the same hook references
      const firstCall = vi.mocked(useFaultTrackingForm).mock.calls[0];
      const secondCall = vi.mocked(useFaultTrackingForm).mock.calls[1];

      expect(firstCall[0]).toBe(secondCall[0]); // Same form state hook
      expect(firstCall[1]).toBe(secondCall[1]); // Same validation hook
    });
  });

  describe("form data handling", () => {
    it("should handle form data updates through setField", () => {
      const mockSetField = vi.fn();
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: null,
          endDate: null,
          startTime: "",
          startPeriod: "AM",
          endTime: "",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: mockSetField,
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      // Call setField
      const testDate = new Date("2024-02-15");
      result.current.setField("startDate", testDate);

      expect(mockSetField).toHaveBeenCalledWith("startDate", testDate);
    });

    it("should handle validation through validate function", () => {
      const mockValidate = vi.fn().mockReturnValue({ comments: "Required" });
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: new Date("2024-02-15"),
          endDate: new Date("2024-02-15"),
          startTime: "10:00",
          startPeriod: "AM",
          endTime: "11:00",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: vi.fn(),
        reset: vi.fn(),
        validate: mockValidate,
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: false,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      const errors = result.current.validate();

      expect(mockValidate).toHaveBeenCalled();
      expect(errors).toEqual({ comments: "Required" });
    });

    it("should handle form reset", () => {
      const mockReset = vi.fn();
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: new Date("2024-02-15"),
          endDate: new Date("2024-02-15"),
          startTime: "10:00",
          startPeriod: "AM",
          endTime: "11:00",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "Test",
        },
        setField: vi.fn(),
        reset: mockReset,
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: { comments: true },
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 1,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      result.current.reset();

      expect(mockReset).toHaveBeenCalled();
    });
  });

  describe("validation integration", () => {
    it("should reflect validation state correctly", () => {
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: null,
          endDate: null,
          startTime: "",
          startPeriod: "AM",
          endTime: "",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {
          startDate: "Start date is required.",
          endDate: "End date is required.",
          comments: "Comments are required.",
        },
        touched: {
          startDate: true,
          comments: true,
        },
        setFieldTouched: vi.fn(),
        isValid: false,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      expect(result.current.isValid).toBe(false);
      expect(result.current.errors).toEqual({
        startDate: "Start date is required.",
        endDate: "End date is required.",
        comments: "Comments are required.",
      });
      expect(result.current.touched).toEqual({
        startDate: true,
        comments: true,
      });
    });

    it("should handle field touched state", () => {
      const mockSetFieldTouched = vi.fn();
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: null,
          endDate: null,
          startTime: "",
          startPeriod: "AM",
          endTime: "",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: mockSetFieldTouched,
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      result.current.setFieldTouched("startDate");

      expect(mockSetFieldTouched).toHaveBeenCalledWith("startDate");
    });
  });

  describe("duration calculation", () => {
    it("should provide duration calculation", () => {
      const mockCalculateDuration = vi.fn().mockReturnValue("2h 30m");
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: new Date("2024-02-15"),
          endDate: new Date("2024-02-15"),
          startTime: "10:00",
          startPeriod: "AM",
          endTime: "12:30",
          endPeriod: "PM",
          startSeconds: "",
          endSeconds: "",
          comments: "Test",
        },
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: mockCalculateDuration,
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      const duration = result.current.calculateDuration();

      expect(mockCalculateDuration).toHaveBeenCalled();
      expect(duration).toBe("2h 30m");
    });

    it("should handle reset trigger changes", () => {
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: null,
          endDate: null,
          startTime: "",
          startPeriod: "AM",
          endTime: "",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: vi.fn(),
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 3,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      expect(result.current.resetTrigger).toBe(3);
    });
  });

  describe("type safety", () => {
    it("should properly type the form data as EditTimingsForm", () => {
      const { result } = renderHook(() => useEditTimingsForm());

      // TypeScript should enforce that formData matches EditTimingsForm interface
      const formData = result.current.formData;

      // These properties should exist and be properly typed
      expect(formData).toHaveProperty("startDate");
      expect(formData).toHaveProperty("endDate");
      expect(formData).toHaveProperty("startTime");
      expect(formData).toHaveProperty("startPeriod");
      expect(formData).toHaveProperty("endTime");
      expect(formData).toHaveProperty("endPeriod");
      expect(formData).toHaveProperty("startSeconds");
      expect(formData).toHaveProperty("endSeconds");
      expect(formData).toHaveProperty("comments");
    });

    it("should enforce EditTimingsForm fields in setField calls", () => {
      const mockSetField = vi.fn();
      vi.mocked(useFaultTrackingForm).mockReturnValue({
        formData: {
          startDate: null,
          endDate: null,
          startTime: "",
          startPeriod: "AM",
          endTime: "",
          endPeriod: "AM",
          startSeconds: "",
          endSeconds: "",
          comments: "",
        },
        setField: mockSetField,
        reset: vi.fn(),
        validate: vi.fn(),
        setErrors: vi.fn(),
        errors: {},
        touched: {},
        setFieldTouched: vi.fn(),
        isValid: true,
        calculateDuration: vi.fn(),
        resetTrigger: 0,
      });

      const { result } = renderHook(() => useEditTimingsForm());

      // These should be valid field names for EditTimingsForm
      result.current.setField("startDate", new Date());
      result.current.setField("startTime", "10:00");
      result.current.setField("startPeriod", "PM");
      result.current.setField("comments", "Test comment");

      expect(mockSetField).toHaveBeenCalledTimes(4);
    });
  });
});

describe("getMinStartDate export", () => {
  it("should re-export getMinStartDate from shared module", () => {
    expect(typeof getMinStartDate).toBe("function");

    const minDate = getMinStartDate();
    expect(minDate).toEqual(new Date("2024-01-15"));
  });
});
