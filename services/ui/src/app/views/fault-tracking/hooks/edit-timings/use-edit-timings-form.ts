import { useEditTimingsFormState } from "./use-edit-timings-form-state";
import { useEditTimingsValidation } from "./use-edit-timings-validation";
import {
  useFaultTrackingForm,
  getMinStartDate,
  type BaseFaultTrackingForm,
} from "../shared/use-fault-tracking-form";

// Re-export for convenience
export { getMinStartDate };

export type EditTimingsForm = BaseFaultTrackingForm;

export type EditTimingsFormField = keyof EditTimingsForm;

export function useEditTimingsForm() {
  return useFaultTrackingForm(
    useEditTimingsFormState,
    useEditTimingsValidation,
  );
}
