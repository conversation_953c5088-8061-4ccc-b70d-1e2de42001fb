import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import { ManualEntryButton } from "./manual-entry-button";

vi.mock("../../../../config/hooks/use-config");

import * as configHooks from "../../../../config/hooks/use-config";
const mockUseFeatureFlag = vi.mocked(configHooks.useFeatureFlag);

vi.mock("./create-manual-entry-button", () => ({
  CreateManualEntryButton: ({ toggleManualEntryModal }: any) => (
    <button
      data-testid="create-manual-entry-button"
      onClick={toggleManualEntryModal}
    >
      Add Manual Entry
    </button>
  ),
}));

describe("ManualEntryButton", () => {
  const mockToggleManualEntryModal = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render the CreateManualEntryButton when feature flag is enabled", () => {
    mockUseFeatureFlag.mockReturnValue({
      enabled: true,
      isLoading: false,
      error: null,
    });

    render(
      <ManualEntryButton toggleManualEntryModal={mockToggleManualEntryModal} />
    );

    // Verify the button is rendered
    const createButton = screen.getByTestId("create-manual-entry-button");
    expect(createButton).toBeDefined();
    expect(createButton.textContent).toBe("Add Manual Entry");

    // Verify useFeatureFlag was called with correct flag name
    expect(mockUseFeatureFlag).toHaveBeenCalledWith(
      "fault-tracking-manual-entry-button"
    );
  });

  it("should not render the CreateManualEntryButton when feature flag is disabled", () => {
    mockUseFeatureFlag.mockReturnValue({
      enabled: false,
      isLoading: false,
      error: null,
    });

    render(
      <ManualEntryButton toggleManualEntryModal={mockToggleManualEntryModal} />
    );

    // Verify the button is not rendered
    const createButton = screen.queryByTestId("create-manual-entry-button");
    expect(createButton).toBeNull();

    // Verify useFeatureFlag was called with correct flag name
    expect(mockUseFeatureFlag).toHaveBeenCalledWith(
      "fault-tracking-manual-entry-button"
    );
  });

  it("should handle feature flag loading state gracefully", () => {
    mockUseFeatureFlag.mockReturnValue({
      enabled: false,
      isLoading: true,
      error: null,
    });

    render(
      <ManualEntryButton toggleManualEntryModal={mockToggleManualEntryModal} />
    );

    // While loading, the flag is false, so button should not render
    const createButton = screen.queryByTestId("create-manual-entry-button");
    expect(createButton).toBeNull();
  });

  it("should handle feature flag error state gracefully", () => {
    mockUseFeatureFlag.mockReturnValue({
      enabled: false,
      isLoading: false,
      error: new Error("Feature flag fetch failed") as any,
    });

    render(
      <ManualEntryButton toggleManualEntryModal={mockToggleManualEntryModal} />
    );

    // On error, the flag defaults to false, so button should not render
    const createButton = screen.queryByTestId("create-manual-entry-button");
    expect(createButton).toBeNull();
  });

  it("should pass the toggleManualEntryModal prop correctly when enabled", () => {
    mockUseFeatureFlag.mockReturnValue({
      enabled: true,
      isLoading: false,
      error: null,
    });

    render(
      <ManualEntryButton toggleManualEntryModal={mockToggleManualEntryModal} />
    );

    const createButton = screen.getByTestId("create-manual-entry-button");
    createButton.click();

    // Verify the callback was passed through correctly
    expect(mockToggleManualEntryModal).toHaveBeenCalledTimes(1);
  });

  it("should use the correct feature flag constant", () => {
    mockUseFeatureFlag.mockReturnValue({
      enabled: true,
      isLoading: false,
      error: null,
    });

    render(
      <ManualEntryButton toggleManualEntryModal={mockToggleManualEntryModal} />
    );

    // Verify the exact flag name from the constant is used
    expect(mockUseFeatureFlag).toHaveBeenCalledWith(
      "fault-tracking-manual-entry-button"
    );
    expect(mockUseFeatureFlag).toHaveBeenCalledTimes(1);
  });
});
