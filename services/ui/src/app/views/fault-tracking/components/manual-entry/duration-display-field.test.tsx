import { render, screen } from "../../../../../test-utils";
import { DurationDisplayField } from "./duration-display-field";

describe("DurationDisplayField", () => {
  const mockCalculateDuration = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render with correct label", () => {
    mockCalculateDuration.mockReturnValue("");

    render(<DurationDisplayField calculateDuration={mockCalculateDuration} />);

    expect(screen.getByText("Duration")).toBeInTheDocument();
  });

  it("should display calculated duration when available", () => {
    const mockDuration = "2 hours 30 minutes";
    mockCalculateDuration.mockReturnValue(mockDuration);

    render(<DurationDisplayField calculateDuration={mockCalculateDuration} />);

    expect(screen.getByText(mockDuration)).toBeInTheDocument();
    expect(mockCalculateDuration).toHaveBeenCalledTimes(1);
  });

  it("should display placeholder text when no duration calculated", () => {
    mockCalculateDuration.mockReturnValue("");

    render(<DurationDisplayField calculateDuration={mockCalculateDuration} />);

    expect(screen.getByText("Enter times to calculate")).toBeInTheDocument();
    expect(mockCalculateDuration).toHaveBeenCalledTimes(1);
  });

  it("should display placeholder text when duration is null", () => {
    mockCalculateDuration.mockReturnValue(null as any);

    render(<DurationDisplayField calculateDuration={mockCalculateDuration} />);

    expect(screen.getByText("Enter times to calculate")).toBeInTheDocument();
  });

  it("should display placeholder text when duration is undefined", () => {
    mockCalculateDuration.mockReturnValue(undefined as any);

    render(<DurationDisplayField calculateDuration={mockCalculateDuration} />);

    expect(screen.getByText("Enter times to calculate")).toBeInTheDocument();
  });

  it("should call calculateDuration function", () => {
    mockCalculateDuration.mockReturnValue("1 hour");

    render(<DurationDisplayField calculateDuration={mockCalculateDuration} />);

    expect(mockCalculateDuration).toHaveBeenCalledTimes(1);
  });

  it("should have proper CSS class structure", () => {
    mockCalculateDuration.mockReturnValue("Test duration");

    const { container } = render(
      <DurationDisplayField calculateDuration={mockCalculateDuration} />,
    );

    expect(container.querySelector(".cds--label")).toBeInTheDocument();
    expect(container.querySelector(".cds--form-item")).toBeInTheDocument();
  });
});
