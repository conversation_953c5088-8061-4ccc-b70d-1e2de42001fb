import { render, screen, userEvent } from "../../../../../test-utils";
import { ManualEntryModal } from "./manual-entry-modal";

// Mock the hooks and utilities
const mockReset = vi.fn();
const mockValidate = vi.fn().mockReturnValue({});
const mockIsValid = vi.fn().mockReturnValue(true);
const mockSetErrors = vi.fn();

vi.mock("../../hooks/manual-entry/use-manual-entry-form", () => {
  return {
    useManualEntryForm: () => ({
      formData: {
        startDate: null,
        endDate: null,
        startTime: "",
        startPeriod: "AM",
        endTime: "",
        endPeriod: "AM",
        startSeconds: "",
        endSeconds: "",
        description: "",
        comments: "",
        section: null,
        equipment: null,
      },
      setField: vi.fn(),
      reset: mockReset,
      validate: mockValidate,
      setErrors: mockSetErrors,
      errors: {},
      touched: {},
      setFieldTouched: vi.fn(),
      isValid: mockIsValid,
      calculateDuration: vi.fn().mockReturnValue(""),
      resetTrigger: 0,
    }),
    getMinStartDate: () => new Date("2024-01-01"),
  };
});

const mockCreateEntry = vi.fn().mockResolvedValue(true);
const mockIsSubmitting = vi.fn().mockReturnValue(false);
const mockIsDisabled = vi.fn().mockReturnValue(false);
const mockDisabledReason = vi.fn().mockReturnValue(undefined);

vi.mock("../../hooks/manual-entry/use-create-manual-entry", () => {
  return {
    useCreateManualEntry: (onSuccess?: () => void) => ({
      createEntry: mockCreateEntry,
      isSubmitting: mockIsSubmitting(),
      isDisabled: mockIsDisabled(),
      disabledReason: mockDisabledReason(),
      onSuccess, // Pass through the onSuccess callback for testing
    }),
  };
});


// Mock Carbon Modal component
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Modal: ({
      open,
      onRequestClose,
      modalHeading,
      primaryButtonText,
      secondaryButtonText,
      onRequestSubmit,
      primaryButtonDisabled,
      children,
    }: {
      open: boolean;
      onRequestClose: () => void;
      modalHeading: string;
      primaryButtonText: string;
      secondaryButtonText: string;
      onRequestSubmit: () => void;
      primaryButtonDisabled: boolean;
      children: React.ReactNode;
    }) =>
      open ? (
        <div data-testid="manual-entry-modal" aria-label={modalHeading}>
          <h2>{modalHeading}</h2>
          <div>{children}</div>
          <button
            data-testid="primary-button"
            type="button"
            onClick={onRequestSubmit}
            disabled={primaryButtonDisabled}
          >
            {primaryButtonText}
          </button>
          <button
            data-testid="secondary-button"
            type="button"
            onClick={onRequestClose}
          >
            {secondaryButtonText}
          </button>
        </div>
      ) : null,
  };
});

// Mock the form component
vi.mock("./manual-entry-form", () => ({
  ManualEntryForm: () => (
    <div data-testid="manual-entry-form">Manual Entry Form</div>
  ),
}));

describe("ManualEntryModal", () => {
  const toggleManualEntryModalMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset mock implementations to defaults
    mockCreateEntry.mockResolvedValue(true);
    mockIsSubmitting.mockReturnValue(false);
    mockIsDisabled.mockReturnValue(false);
    mockDisabledReason.mockReturnValue(undefined);
    mockValidate.mockReturnValue({});
    mockIsValid.mockReturnValue(true);
  });

  it("should render when modal is open", () => {
    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    expect(screen.getByTestId("manual-entry-modal")).toBeInTheDocument();
  });

  it("should render when modal is open with onSuccess prop", () => {
    const mockOnSuccess = vi.fn();
    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
        onSuccess={mockOnSuccess}
      />,
    );

    expect(screen.getByTestId("manual-entry-modal")).toBeInTheDocument();
  });

  it("should not render when modal is closed", () => {
    render(
      <ManualEntryModal
        isManualEntryModalOpen={false}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    expect(screen.queryByTestId("manual-entry-modal")).not.toBeInTheDocument();
  });

  it("should render with the correct heading", () => {
    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    expect(
      screen.getByRole("heading", { name: "Create a Manual Alarm" }),
    ).toBeInTheDocument();
  });

  it("should render the subtitle text", () => {
    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    expect(
      screen.getByText(/Adjust the date range, start and end time/),
    ).toBeInTheDocument();
  });

  it("should render the manual entry form", () => {
    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    expect(screen.getByTestId("manual-entry-form")).toBeInTheDocument();
  });

  it("should call toggleManualEntryModal when cancel button is clicked", async () => {
    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    const cancelButton = screen.getByTestId("secondary-button");
    await userEvent.click(cancelButton);

    expect(toggleManualEntryModalMock).toHaveBeenCalledTimes(1);
  });

  it("should render primary button with text 'Save'", () => {
    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    expect(screen.getByTestId("primary-button")).toHaveTextContent("Save");
  });

  it("should render secondary button with text 'Cancel'", () => {
    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    expect(screen.getByTestId("secondary-button")).toHaveTextContent("Cancel");
  });

  it("should enable primary button when form is valid", () => {
    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    expect(screen.getByTestId("primary-button")).not.toBeDisabled();
  });

  it("should handle form submission successfully", async () => {
    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    const saveButton = screen.getByTestId("primary-button");
    await userEvent.click(saveButton);

    // Since form is valid by default in our mocks, modal should close
    expect(toggleManualEntryModalMock).toHaveBeenCalledTimes(1);
  });

  it("should call onSuccess callback when form submission succeeds", async () => {
    const mockOnSuccess = vi.fn();
    
    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
        onSuccess={mockOnSuccess}
      />,
    );

    const saveButton = screen.getByTestId("primary-button");
    await userEvent.click(saveButton);

    // Verify that createEntry was called and modal closed
    expect(mockCreateEntry).toHaveBeenCalledTimes(1);
    expect(toggleManualEntryModalMock).toHaveBeenCalledTimes(1);
  });

  it("should not call onSuccess when submission fails", async () => {
    const mockOnSuccess = vi.fn();
    // Mock createEntry to return false (failure)
    mockCreateEntry.mockResolvedValue(false);

    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
        onSuccess={mockOnSuccess}
      />,
    );

    const saveButton = screen.getByTestId("primary-button");
    await userEvent.click(saveButton);

    // Modal should not close when submission fails
    expect(toggleManualEntryModalMock).not.toHaveBeenCalled();
  });

  it("should not submit when form has validation errors", async () => {
    // Mock validation to return errors
    mockValidate.mockReturnValue({ description: "Description is required" });
    mockIsValid.mockReturnValue(false);

    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    const saveButton = screen.getByTestId("primary-button");
    await userEvent.click(saveButton);

    // createEntry should not be called when form has validation errors
    expect(mockCreateEntry).not.toHaveBeenCalled();
    expect(toggleManualEntryModalMock).not.toHaveBeenCalled();
  });

  it("should disable primary button when submitting", () => {
    // Mock isSubmitting to be true
    mockIsSubmitting.mockReturnValue(true);

    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    expect(screen.getByTestId("primary-button")).toBeDisabled();
  });

  it("should call toggle when cancel button is clicked", async () => {
    render(
      <ManualEntryModal
        isManualEntryModalOpen={true}
        toggleManualEntryModal={toggleManualEntryModalMock}
      />,
    );

    const cancelButton = screen.getByTestId("secondary-button");
    await userEvent.click(cancelButton);

    expect(toggleManualEntryModalMock).toHaveBeenCalledTimes(1);
  });

  describe("disabled state behavior", () => {
    it("should disable primary button when manual entry is disabled", () => {
      mockIsDisabled.mockReturnValue(true);
      mockDisabledReason.mockReturnValue("Timezone configuration is required");

      render(
        <ManualEntryModal
          isManualEntryModalOpen={true}
          toggleManualEntryModal={toggleManualEntryModalMock}
        />,
      );

      expect(screen.getByTestId("primary-button")).toBeDisabled();
    });

    it("should show error message when disabled", () => {
      mockIsDisabled.mockReturnValue(true);
      mockDisabledReason.mockReturnValue("Timezone configuration is required for manual entries");

      render(
        <ManualEntryModal
          isManualEntryModalOpen={true}
          toggleManualEntryModal={toggleManualEntryModalMock}
        />,
      );

      expect(screen.getByText("Timezone configuration is required for manual entries")).toBeInTheDocument();
    });

    it("should show normal subtitle when not disabled", () => {
      mockIsDisabled.mockReturnValue(false);
      mockDisabledReason.mockReturnValue(undefined);

      render(
        <ManualEntryModal
          isManualEntryModalOpen={true}
          toggleManualEntryModal={toggleManualEntryModalMock}
        />,
      );

      expect(
        screen.getByText(/Adjust the date range, start and end time/),
      ).toBeInTheDocument();
      expect(
        screen.queryByText("Timezone configuration is required for manual entries")
      ).not.toBeInTheDocument();
    });

    it("should enable primary button when not disabled and form is valid", () => {
      mockIsDisabled.mockReturnValue(false);
      mockIsSubmitting.mockReturnValue(false);
      mockIsValid.mockReturnValue(true);

      render(
        <ManualEntryModal
          isManualEntryModalOpen={true}
          toggleManualEntryModal={toggleManualEntryModalMock}
        />,
      );

      expect(screen.getByTestId("primary-button")).not.toBeDisabled();
    });

    it("should disable primary button when disabled even if form is valid", () => {
      mockIsDisabled.mockReturnValue(true);
      mockIsSubmitting.mockReturnValue(false);
      mockIsValid.mockReturnValue(true);

      render(
        <ManualEntryModal
          isManualEntryModalOpen={true}
          toggleManualEntryModal={toggleManualEntryModalMock}
        />,
      );

      expect(screen.getByTestId("primary-button")).toBeDisabled();
    });
  });
});
