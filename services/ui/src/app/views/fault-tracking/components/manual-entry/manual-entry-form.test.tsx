import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { expect } from "vitest";
import { ManualEntryForm } from "./manual-entry-form";
import type { ManualEntryForm as FormData } from "../../hooks/manual-entry/use-manual-entry-form";

// Mock the sub-components
vi.mock("../shared/time-picker-field", () => ({
  TimePickerField: ({ id, label, value, period, selectedDate }: any) => (
    <div data-testid={id}>
      <label>{label}</label>
      <span data-testid={`${id}-time-value`}>{value}</span>
      <span data-testid={`${id}-period-value`}>{period}</span>
      <span data-testid={`${id}-selected-date`}>
        {selectedDate ? selectedDate.toISOString() : "null"}
      </span>
    </div>
  ),
}));

vi.mock("../shared/seconds-input-field", () => ({
  SecondsInputField: ({ id, value, label }: any) => (
    <div data-testid={id}>
      <label>{label}</label>
      <span data-testid={`${id}-value`}>{value}</span>
    </div>
  ),
}));

vi.mock("./included-default-toggle", () => ({
  IncludedDefaultToggle: () => (
    <div data-testid="included-default-toggle">Included Toggle</div>
  ),
}));

vi.mock("./duration-display-field", () => ({
  DurationDisplayField: ({ calculateDuration }: any) => (
    <div data-testid="duration-display-field">
      Duration: {calculateDuration()}
    </div>
  ),
}));

vi.mock("./section-equipment-selector", () => ({
  SectionEquipmentSelector: ({
    onSectionChange,
    onEquipmentChange,
    sectionError,
    equipmentError,
    resetTrigger,
  }: any) => (
    <div data-testid="section-equipment-selector">
      <button
        onClick={() => onSectionChange({ pk: 1, name: "Section A" })}
        data-testid="select-section"
      >
        Select Section
      </button>
      <button
        onClick={() => onEquipmentChange({ pk: 10, name: "Equipment 1" })}
        data-testid="select-equipment"
      >
        Select Equipment
      </button>
      {sectionError && <span data-testid="section-error">{sectionError}</span>}
      {equipmentError && (
        <span data-testid="equipment-error">{equipmentError}</span>
      )}
      <span data-testid="reset-trigger">{resetTrigger}</span>
    </div>
  ),
}));

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, fallback: string) => fallback || key,
  }),
}));

describe("ManualEntryForm", () => {
  const mockFormData: FormData = {
    startDate: new Date("2024-01-15"),
    endDate: new Date("2024-01-15"),
    startTime: "10:00",
    startPeriod: "AM",
    endTime: "11:00",
    endPeriod: "AM",
    startSeconds: "30:500",
    endSeconds: "45:750",
    description: "Test alarm description",
    comments: "Test alarm comments",
    section: null,
    equipment: null,
  };

  const mockProps = {
    formData: mockFormData,
    setField: vi.fn(),
    errors: {},
    minStartDate: new Date("2024-01-01"),
    validate: vi.fn(),
    setErrors: vi.fn(),
    touched: {},
    setFieldTouched: vi.fn(),
    calculateDuration: vi.fn(() => "1 hour 15 minutes"),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render all form components", () => {
    render(<ManualEntryForm {...mockProps} />);

    // Check that all major components are rendered
    expect(screen.getByTestId("section-equipment-selector")).toBeDefined();
    expect(screen.getByTestId("included-default-toggle")).toBeDefined();
    expect(screen.getByTestId("start-time-picker")).toBeDefined();
    expect(screen.getByTestId("end-time-picker")).toBeDefined();
    expect(screen.getByTestId("start-seconds")).toBeDefined();
    expect(screen.getByTestId("end-seconds")).toBeDefined();
    expect(screen.getByTestId("duration-display-field")).toBeDefined();
    expect(screen.getByTestId("alarm-description")).toBeDefined();
    expect(screen.getByTestId("alarm-comments")).toBeDefined();
  });

  it("should display form data correctly", () => {
    render(<ManualEntryForm {...mockProps} />);

    // Check time picker values
    expect(screen.getByTestId("start-time-picker-time-value").textContent).toBe(
      "10:00",
    );
    expect(
      screen.getByTestId("start-time-picker-period-value").textContent,
    ).toBe("AM");
    expect(screen.getByTestId("end-time-picker-time-value").textContent).toBe(
      "11:00",
    );
    expect(screen.getByTestId("end-time-picker-period-value").textContent).toBe(
      "AM",
    );

    // Check seconds inputs
    expect(screen.getByTestId("start-seconds-value").textContent).toBe(
      "30:500",
    );
    expect(screen.getByTestId("end-seconds-value").textContent).toBe("45:750");

    // Check text areas
    expect(
      (screen.getByTestId("alarm-description") as HTMLTextAreaElement).value,
    ).toBe("Test alarm description");
    expect(
      (screen.getByTestId("alarm-comments") as HTMLTextAreaElement).value,
    ).toBe("Test alarm comments");

    // Check duration display
    expect(screen.getByTestId("duration-display-field").textContent).toContain(
      "Duration: 1 hour 15 minutes",
    );
  });

  it("should display form labels correctly", () => {
    render(<ManualEntryForm {...mockProps} />);

    expect(screen.getByText("Start Date")).toBeDefined();
    expect(screen.getByText("End Date")).toBeDefined();
    expect(screen.getByText("Start Time")).toBeDefined();
    expect(screen.getByText("End Time")).toBeDefined();
    expect(screen.getAllByText("Seconds & Milliseconds")).toHaveLength(2); // Two instances
    expect(screen.getByText("Alarm Description")).toBeDefined();
    expect(screen.getByText("Alarm Comments")).toBeDefined();
  });

  it("should handle text area changes", async () => {
    const user = userEvent.setup();
    render(<ManualEntryForm {...mockProps} />);

    const descriptionTextArea = screen.getByTestId("alarm-description");
    await user.type(descriptionTextArea, "Updated description");

    // setField should have been called multiple times for each character
    expect(mockProps.setField).toHaveBeenCalled();
    expect(mockProps.setField).toHaveBeenCalledWith(
      "description",
      expect.any(String),
    );
  });

  it("should handle text area blur events", async () => {
    const user = userEvent.setup();
    render(<ManualEntryForm {...mockProps} />);

    const descriptionTextArea = screen.getByTestId("alarm-description");
    await user.click(descriptionTextArea);
    await user.tab(); // Blur the input

    expect(mockProps.setFieldTouched).toHaveBeenCalledWith("description");
    expect(mockProps.validate).toHaveBeenCalled();
  });

  it("should show validation errors when touched", () => {
    const propsWithErrors = {
      ...mockProps,
      errors: {
        description: "Description is required",
        comments: "Comments are required",
      },
      touched: {
        description: true,
        comments: true,
      },
    };

    render(<ManualEntryForm {...propsWithErrors} />);

    expect(screen.getByText("Description is required")).toBeDefined();
    expect(screen.getByText("Comments are required")).toBeDefined();
  });

  it("should only show errors for touched fields", () => {
    const propsWithUntouchedErrors = {
      ...mockProps,
      errors: {
        description: "Description is required",
        comments: "Comments are required",
      },
      touched: {
        description: true,
        // comments is not touched
      },
    };

    render(<ManualEntryForm {...propsWithUntouchedErrors} />);

    // Should show error for touched field
    expect(screen.getByText("Description is required")).toBeDefined();

    // Should not show error for untouched field
    expect(screen.queryByText("Comments are required")).toBeNull();
  });

  it("should have required attributes on text areas", () => {
    render(<ManualEntryForm {...mockProps} />);

    const descriptionTextArea = screen.getByTestId("alarm-description");
    const commentsTextArea = screen.getByTestId("alarm-comments");

    expect(descriptionTextArea.hasAttribute("required")).toBe(true);
    expect(commentsTextArea.hasAttribute("required")).toBe(true);
  });

  it("should have correct text area configurations", () => {
    render(<ManualEntryForm {...mockProps} />);

    const descriptionTextArea = screen.getByTestId("alarm-description");
    const commentsTextArea = screen.getByTestId("alarm-comments");

    expect(descriptionTextArea.getAttribute("maxlength")).toBe("350");
    expect(commentsTextArea.getAttribute("maxlength")).toBe("350");
    expect(descriptionTextArea.getAttribute("rows")).toBe("4");
    expect(commentsTextArea.getAttribute("rows")).toBe("4");
  });

  it("should render with proper form structure", () => {
    render(<ManualEntryForm {...mockProps} />);

    // Check for form element by class instead of role
    const formElement = document.querySelector(".cds--form");
    expect(formElement).toBeDefined();
  });

  it("should pass calculateDuration to DurationDisplayField", () => {
    render(<ManualEntryForm {...mockProps} />);

    expect(screen.getByTestId("duration-display-field").textContent).toContain(
      "Duration: 1 hour 15 minutes",
    );
    expect(mockProps.calculateDuration).toHaveBeenCalled();
  });

  it("should handle empty form data gracefully", () => {
    const emptyFormData: FormData = {
      startDate: null,
      endDate: null,
      startTime: "",
      startPeriod: "AM",
      endTime: "",
      endPeriod: "AM",
      startSeconds: "",
      endSeconds: "",
      description: "",
      comments: "",
      section: null,
      equipment: null,
    };

    const emptyProps = {
      ...mockProps,
      formData: emptyFormData,
      calculateDuration: vi.fn(() => ""),
    };

    render(<ManualEntryForm {...emptyProps} />);

    expect(screen.getByTestId("start-time-picker-time-value").textContent).toBe(
      "",
    );
    expect(screen.getByTestId("end-time-picker-time-value").textContent).toBe(
      "",
    );
    expect(screen.getByTestId("start-seconds-value").textContent).toBe("");
    expect(screen.getByTestId("end-seconds-value").textContent).toBe("");
    expect(
      (screen.getByTestId("alarm-description") as HTMLTextAreaElement).value,
    ).toBe("");
    expect(
      (screen.getByTestId("alarm-comments") as HTMLTextAreaElement).value,
    ).toBe("");
  });

  it("should integrate with sub-components properly", () => {
    render(<ManualEntryForm {...mockProps} />);

    // Verify time picker components receive correct props
    expect(screen.getByTestId("start-time-picker-time-value").textContent).toBe(
      "10:00",
    );
    expect(
      screen.getByTestId("start-time-picker-period-value").textContent,
    ).toBe("AM");
    expect(screen.getByTestId("end-time-picker-time-value").textContent).toBe(
      "11:00",
    );
    expect(screen.getByTestId("end-time-picker-period-value").textContent).toBe(
      "AM",
    );

    // Verify seconds input components receive correct props
    expect(screen.getByTestId("start-seconds-value").textContent).toBe(
      "30:500",
    );
    expect(screen.getByTestId("end-seconds-value").textContent).toBe("45:750");

    // Verify duration display gets called
    expect(mockProps.calculateDuration).toHaveBeenCalled();
  });

  it("should handle section and equipment selection", async () => {
    const user = userEvent.setup();
    render(<ManualEntryForm {...mockProps} />);

    // Test section selection
    await user.click(screen.getByTestId("select-section"));
    expect(mockProps.setField).toHaveBeenCalledWith("section", {
      pk: 1,
      name: "Section A",
    });

    // Test equipment selection
    await user.click(screen.getByTestId("select-equipment"));
    expect(mockProps.setField).toHaveBeenCalledWith("equipment", {
      pk: 10,
      name: "Equipment 1",
    });
  });

  it("should pass reset trigger to section equipment selector", () => {
    render(<ManualEntryForm {...mockProps} resetTrigger={5} />);

    expect(screen.getByTestId("reset-trigger").textContent).toBe("5");
  });

  it("should pass selectedDate props to TimePickerField components", () => {
    const startDate = new Date("2024-01-15T10:00:00Z");
    const endDate = new Date("2024-01-16T15:30:00Z");

    const formDataWithDates = {
      ...mockProps.formData,
      startDate,
      endDate,
    };

    render(<ManualEntryForm {...mockProps} formData={formDataWithDates} />);

    // Verify start time picker receives startDate
    const startDateElement = screen.getByTestId(
      "start-time-picker-selected-date",
    );
    expect(startDateElement.textContent).toBe(startDate.toISOString());

    // Verify end time picker receives endDate
    const endDateElement = screen.getByTestId("end-time-picker-selected-date");
    expect(endDateElement.textContent).toBe(endDate.toISOString());
  });

  it("should handle null dates in TimePickerField components", () => {
    const formDataWithNullDates = {
      ...mockProps.formData,
      startDate: null,
      endDate: null,
    };

    render(<ManualEntryForm {...mockProps} formData={formDataWithNullDates} />);

    // Verify time pickers handle null dates
    const startDateElement = screen.getByTestId(
      "start-time-picker-selected-date",
    );
    expect(startDateElement.textContent).toBe("null");

    const endDateElement = screen.getByTestId("end-time-picker-selected-date");
    expect(endDateElement.textContent).toBe("null");
  });
});
