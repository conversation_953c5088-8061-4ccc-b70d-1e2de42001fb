.formRow {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
}

.timeSection {
    display: flex;
    flex-direction: row;
    min-width: 300px;
    gap: 0.5rem;
}

@media (max-width: 768px) {
    .formRow {
        flex-direction: column;
    }
    
    .timeSection {
        min-width: unset;
        width: 100%;
    }
}
.durationDisplay {
    padding: 0.5rem 1rem;
        background-color: var(--cds-field);
        display: flex;
        align-items: center
}
.fullWidthDatePicker {
    width: 100%;
}