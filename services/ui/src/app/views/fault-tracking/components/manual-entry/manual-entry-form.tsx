import {
  Form,
  DatePicker,
  DatePickerInput,
  Text<PERSON><PERSON>,
  Stack,
} from "@carbon/react";
import { useRef } from "react";
import { useManualEntryForm } from "../../hooks/manual-entry/use-manual-entry-form";
import { TimePickerField, SecondsInputField } from "../shared";
import styles from "./manual-entry-form.module.scss";
import { IncludedDefaultToggle } from "./included-default-toggle";
import { DurationDisplayField } from "./duration-display-field";
import {
  SectionEquipmentSelector,
  SectionEquipmentSelectorRef,
} from "./section-equipment-selector";
import { useTranslation } from "react-i18next";

interface ManualEntryFormProps {
  formData: ReturnType<typeof useManualEntryForm>["formData"];
  setField: ReturnType<typeof useManualEntryForm>["setField"];
  errors: Partial<
    Record<keyof ReturnType<typeof useManualEntryForm>["formData"], string>
  >;
  minStartDate: Date;
  validate: ReturnType<typeof useManualEntryForm>["validate"];
  setErrors: (
    errors: Partial<
      Record<keyof ReturnType<typeof useManualEntryForm>["formData"], string>
    >,
  ) => void;
  touched: Partial<
    Record<keyof ReturnType<typeof useManualEntryForm>["formData"], boolean>
  >;
  setFieldTouched: (
    field: keyof ReturnType<typeof useManualEntryForm>["formData"],
  ) => void;
  calculateDuration: ReturnType<typeof useManualEntryForm>["calculateDuration"];
  resetTrigger?: number;
}

export function ManualEntryForm({
  formData,
  setField,
  errors,
  minStartDate,
  validate,
  touched,
  setFieldTouched,
  calculateDuration,
  resetTrigger = 0,
}: ManualEntryFormProps) {
  const selectorRef = useRef<SectionEquipmentSelectorRef>(null);
  const today = new Date();
  const handleBlur = (field: keyof typeof formData) => {
    setFieldTouched(field);
    validate();
  };

  const { t } = useTranslation();

  return (
    <Form>
      <Stack gap={"1rem"}>
        <IncludedDefaultToggle />
        <Stack orientation="horizontal" className={styles.formRow}>
          {/* NOTE: Carbon auto-flips start/end dates in range mode if end < start,
// but the visual range highlight may not appear when dates are entered manually.
// This is a known Carbon UI bug */}
          <div style={{ width: "100%" }}>
            <DatePicker
              key={`datepicker-${resetTrigger}`}
              // ensures the DatePicker only ever receives an array of valid Date objects
              value={[formData.startDate, formData.endDate].filter(
                (date): date is Date => date instanceof Date,
              )}
              onChange={(dates) => {
                setField("startDate", dates[0] ?? null);
                setField("endDate", dates[1] ?? null);
              }}
              datePickerType="range"
              minDate={minStartDate}
              closeOnSelect={true}
              maxDate={today}
              className={styles.fullWidthDatePicker}
            >
              <DatePickerInput
                id="start-date"
                labelText={t("faultTracking.startDate", "Start Date")}
                placeholder="yyyy-mm-dd"
                invalid={touched.startDate && !!errors.startDate}
                invalidText={touched.startDate ? errors.startDate : undefined}
                style={{ width: "100%" }}
              />
              <DatePickerInput
                id="end-date"
                labelText="End Date"
                placeholder="yyyy-mm-dd"
                invalid={touched.endDate && !!errors.endDate}
                invalidText={touched.endDate ? errors.endDate : undefined}
                style={{ width: "100%" }}
              />
            </DatePicker>
          </div>
        </Stack>
        <div className={styles.formRow}>
          <div className={styles.timeSection}>
            <TimePickerField
              id="start-time-picker"
              label={t("faultTracking.startTime", "Start Time")}
              value={formData.startTime}
              period={formData.startPeriod}
              onTimeChange={(value) => setField("startTime", value)}
              onPeriodChange={(value) => setField("startPeriod", value)}
              error={errors.startTime}
              touched={touched.startTime}
              onTimeBlur={() => handleBlur("startTime")}
              onPeriodBlur={() => handleBlur("startPeriod")}
              selectedDate={formData.startDate}
            />
            <SecondsInputField
              id="start-seconds"
              value={formData.startSeconds}
              onChange={(value) => setField("startSeconds", value)}
              error={errors.startSeconds}
              touched={touched.startSeconds}
              onBlur={() => handleBlur("startSeconds")}
              label={t(
                "faultTracking.secondsMilliseconds",
                "Seconds & Milliseconds",
              )}
            />
          </div>

          <div className={styles.timeSection}>
            <TimePickerField
              id="end-time-picker"
              label={t("faultTracking.endTime", "End Time")}
              value={formData.endTime}
              period={formData.endPeriod}
              onTimeChange={(value) => setField("endTime", value)}
              onPeriodChange={(value) => setField("endPeriod", value)}
              error={errors.endTime}
              touched={touched.endTime}
              onTimeBlur={() => handleBlur("endTime")}
              onPeriodBlur={() => handleBlur("endPeriod")}
              selectedDate={formData.endDate}
            />
            <SecondsInputField
              id="end-seconds"
              value={formData.endSeconds}
              onChange={(value) => setField("endSeconds", value)}
              error={errors.endSeconds}
              touched={touched.endSeconds}
              onBlur={() => handleBlur("endSeconds")}
              label={t(
                "faultTracking.secondsMilliseconds",
                "Seconds & Milliseconds",
              )}
            />
          </div>
        </div>

        <DurationDisplayField calculateDuration={calculateDuration} />
        <Stack orientation="horizontal" className={styles.formRow}>
          <SectionEquipmentSelector
            ref={selectorRef}
            onSectionChange={(section) => {
              setField("section", section);
            }}
            onEquipmentChange={(equipment) => {
              setField("equipment", equipment);
            }}
            onSectionBlur={() => setFieldTouched("section")}
            onEquipmentBlur={() => setFieldTouched("equipment")}
            sectionError={errors.section}
            equipmentError={errors.equipment}
            sectionTouched={touched.section}
            equipmentTouched={touched.equipment}
            resetTrigger={resetTrigger}
          />
        </Stack>
        <TextArea
          id="description"
          data-testid="alarm-description"
          labelText={t("faultTracking.alarmDescription", "Alarm Description")}
          placeholder={t("faultTracking.enterDescription", "Enter description")}
          value={formData.description}
          onChange={(e) => setField("description", e.target.value)}
          invalid={touched.description && !!errors.description}
          invalidText={touched.description ? errors.description : undefined}
          onBlur={() => handleBlur("description")}
          required
          maxCount={350}
          rows={4}
          enableCounter
        />

        <TextArea
          id="comments"
          data-testid="alarm-comments"
          labelText={t("faultTracking.comments", "Alarm Comments")}
          placeholder={t("faultTracking.enterComments", "Enter comments")}
          value={formData.comments}
          onChange={(e) => setField("comments", e.target.value)}
          invalid={touched.comments && !!errors.comments}
          invalidText={touched.comments ? errors.comments : undefined}
          onBlur={() => handleBlur("comments")}
          required
          maxCount={350}
          rows={4}
          enableCounter
        />
      </Stack>
    </Form>
  );
}
