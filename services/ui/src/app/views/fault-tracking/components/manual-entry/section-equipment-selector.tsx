import { ComboBox, InlineLoading, <PERSON><PERSON>, But<PERSON> } from "@carbon/react";
import { Close } from "@carbon/icons-react";
import { useTranslation } from "react-i18next";
import { forwardRef, useImperativeHandle, useEffect } from "react";
import { useSectionEquipmentSelector } from "../../hooks/manual-entry/use-section-equipment-selector";
import styles from "./section-equipment-selector.module.scss";
import type { Section, Equipment } from "../../types/types";

interface SectionEquipmentSelectorProps {
  onSectionChange: (section: Section | null) => void;
  onEquipmentChange: (equipment: Equipment | null) => void;
  onSectionBlur?: () => void;
  onEquipmentBlur?: () => void;
  sectionError?: string;
  equipmentError?: string;
  sectionTouched?: boolean;
  equipmentTouched?: boolean;
  resetTrigger?: number; // used to trigger resets from parent
}

export interface SectionEquipmentSelectorRef {
  reset: () => void;
}

export const SectionEquipmentSelector = forwardRef<
  SectionEquipmentSelectorRef,
  SectionEquipmentSelectorProps
>(function SectionEquipmentSelector({
  onSectionChange,
  onEquipmentChange,
  onSectionBlur,
  onEquipmentBlur,
  sectionError,
  equipmentError,
  sectionTouched = false,
  equipmentTouched = false,
  resetTrigger = 0,
}, ref) {
  const { t } = useTranslation();
  
  const {
    sections,
    equipment,
    selectedSection,
    selectedEquipment,
    isLoading,
    isSectionLoading,
    error,
    onSectionChange: internalOnSectionChange,
    onEquipmentChange: internalOnEquipmentChange,
    reset,
  } = useSectionEquipmentSelector();

  const handleSectionChange = ({ selectedItem }: { selectedItem: Section | null | undefined }) => {
    internalOnSectionChange(selectedItem || null);
    onSectionChange(selectedItem || null);
  };

  const handleEquipmentChange = ({ selectedItem }: { selectedItem: Equipment | null | undefined }) => {
    internalOnEquipmentChange(selectedItem || null, (autoSetSection: Section | null) => {
      // when section is auto-set from equipment selection...update the form
      onSectionChange(autoSetSection);
    });
    onEquipmentChange(selectedItem || null);
  };

  const handleReset = () => {
    reset();
    onSectionChange(null);
    onEquipmentChange(null);
  };

  useImperativeHandle(ref, () => ({
    reset: handleReset,
  }));

  // reset when resetTrigger changes
  useEffect(() => {
    if (resetTrigger > 0) {
      handleReset();
    }
  }, [resetTrigger]);

  if (isLoading) {
    return (
      <Stack gap="1rem">
        <InlineLoading description={t("faultTracking.loadingSectionsEquipment", "Loading sections and equipment...")} />
      </Stack>
    );
  }

  if (error) {
    return (
      <Stack gap="1rem">
        <div style={{ color: "var(--cds-text-error)" }}>
          {error}
        </div>
      </Stack>
    );
  }
  return (
<Stack orientation="horizontal" className={styles.parentContainer}>
      <div className={styles.childContainer}>

      {selectedEquipment ? (
        // show read-only section display when equipment is selected
        <div>
          <label className="cds--label">{t("faultTracking.section", "Section *")}</label>
          <div 
          className="cds--form-item"
          >
            <div className={styles.sectionDisplay}>
              {isSectionLoading ? (
                <InlineLoading description={t("faultTracking.loadingSection", "Loading section...")} />
              ) : (
                <span>{selectedSection?.name || t("faultTracking.noSectionFound", "No section found")}</span>
              )}
              <Button
                kind="ghost"
                size="sm"
                hasIconOnly
                iconDescription={t("faultTracking.clearSelection", "Clear selection")}
                onClick={handleReset}
              >
                <Close size={16} />
              </Button>
            </div>
          </div>
        </div>
      ) : (
        // show dropdown when no equipment is selected
        <ComboBox
          id="section-combobox"
          titleText={t("faultTracking.section", "Section *")}
          placeholder={t("faultTracking.searchSections", "Search sections...")}
          items={sections}
          itemToString={(item) => item?.name || ""}
          selectedItem={selectedSection}
          onChange={handleSectionChange}
          onBlur={onSectionBlur}
          invalid={sectionTouched && !!sectionError}
          invalidText={sectionTouched ? sectionError : undefined}
          size="md"
        />
      )}
      </div>
<div className={styles.childContainer}>
      <ComboBox
        id="equipment-combobox"
        titleText={t("faultTracking.equipment", "Equipment *")}
        placeholder={t("faultTracking.searchEquipment", "Search equipment...")}
        items={equipment}
        itemToString={(item) => item?.name || ""}
        selectedItem={selectedEquipment}
        onChange={handleEquipmentChange}
        onBlur={onEquipmentBlur}
        invalid={equipmentTouched && !!equipmentError}
        invalidText={equipmentTouched ? equipmentError : undefined}
        size="md"
      />
      </div>
    </Stack>
  );
});
