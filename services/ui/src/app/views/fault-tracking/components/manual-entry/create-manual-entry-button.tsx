import { But<PERSON>, Tooltip } from "@carbon/react";
import { Add } from "@carbon/icons-react";
import { useTranslation } from "react-i18next";
import { useCreateManualEntry } from "../../hooks/manual-entry/use-create-manual-entry";

export function CreateManualEntryButton({
  toggleManualEntryModal,
}: {
  toggleManualEntryModal: () => void;
}) {
  const { t } = useTranslation();
  const { isDisabled, disabledReason } = useCreateManualEntry();

  const button = (
    <Button
      kind="primary"
      renderIcon={Add}
      onClick={toggleManualEntryModal}
      disabled={isDisabled}
      data-testid="button-add-manual-entry"
    >
      {t("faultTracking.addManualEntry", "Add Manual Entry")}
    </Button>
  );

  // Wrap with tooltip if disabled
  if (isDisabled && disabledReason) {
    return (
      <Tooltip label={disabledReason}>
        {button}
      </Tooltip>
    );
  }

  return button;
}
