import { Datagrid } from "../../../components/datagrid/datagrid";
import type {
  RowSelectionState,
  OnChangeFn,
  ColumnDef,
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import type { FilterState } from "../../../components/datagrid/types";
import { FaultAlarm } from "../types/types";
import type { ReactNode } from "react";

type BatchAction = {
  label: string;
  onClick: () => void;
  disabled?: boolean;
  tooltip?: string;
};

type FaultTrackingTableProps = {
  tableKey: number;
  columns: ColumnDef<FaultAlarm, string>[];
  data: FaultAlarm[];
  rowCount: number;
  isLoading: boolean;
  isFetching: boolean;
  error?: unknown;
  pagination: PaginationState;
  setPagination: (pagination: PaginationState) => void;
  sorting: SortingState;
  setSorting: (sorting: SortingState) => void;
  setColumnFilters: (filters: ColumnFiltersState) => void;
  setGlobalFilter: (globalFilter: string) => void;
  rowSelection: RowSelectionState;
  onRowSelectionChange: OnChangeFn<RowSelectionState>;
  onRefreshClick: () => void;
  actionBarItems?: ReactNode;
  batchActions?: BatchAction[];
};

export function FaultTrackingTable({
  tableKey,
  columns,
  data,
  rowCount,
  isLoading,
  isFetching,
  error,
  pagination,
  setPagination,
  sorting,
  setSorting,
  setColumnFilters,
  setGlobalFilter,
  rowSelection,
  onRowSelectionChange,
  onRefreshClick,
  actionBarItems,
  batchActions,
}: FaultTrackingTableProps) {
  const handlePageChange = (newPagination: PaginationState) => {
    setPagination(newPagination);
  };

  const handleSort = (newSorting: SortingState) => {
    setSorting(newSorting);
  };

  const handleFilter = (newFilters: FilterState) => {
    const newColumnFilters: ColumnFiltersState = Object.entries(
      newFilters.filters,
    ).map(([id, value]) => ({
      id,
      value,
    }));
    setColumnFilters(newColumnFilters);
    setGlobalFilter(newFilters.globalFilter);
  };

  return (
    <Datagrid
      key={tableKey}
      columns={columns}
      data={data}
      mode="server"
      totalRows={rowCount}
      isLoading={isLoading || isFetching}
      error={error ? String(error) : undefined}
      onPageChange={handlePageChange}
      onSort={handleSort}
      onFilter={handleFilter}
      initialPagination={pagination}
      initialSorting={sorting}
      enableSelection={false}
      rowSelection={rowSelection}
      showExportButton={false}
      onRowSelectionChange={onRowSelectionChange}
      showRefreshButton={true}
      onRefreshClick={onRefreshClick}
      actionBarItems={actionBarItems}
      batchActions={batchActions}
    />
  );
}
