import { Modal } from "@carbon/react";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { useConfigSetting } from "../../../../config/hooks/use-config";
import { useNotification } from "../../../../components/toast/use-notification";
import { Logger } from "../../../../utils/logger";
import { useEditTimingsForm } from "../../hooks/edit-timings/use-edit-timings-form";
import { formatEditTimingsForAPI } from "../../utils/edit-timings-api-formatter";

const logger = new Logger("EditTimingsModal");

export function EditTimingsModal({
  isEditTimingsModalOpen,
  toggleEditTimingsModal,
}: {
  isEditTimingsModalOpen: boolean;
  toggleEditTimingsModal: () => void;
}) {
  const { t } = useTranslation();
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const { formData, reset, validate, setErrors, isValid } =
    useEditTimingsForm();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { success } = useNotification();

  const handleSubmit = () => {
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    logger.info("Form data:", formData);
    const facilityTimezone =
      (timezoneConfig?.value as string) ?? "America/New_York";
    const apiPayload = formatEditTimingsForAPI(formData, facilityTimezone);
    logger.info("API payload:", apiPayload);
    setErrors({});
    setIsSubmitting(true);

    // TODO: submit logic
    logger.info("Submitting");

    // On success:
    success(
      t(
        "faultTracking.manualEntrySavedSuccessfully",
        "Manual entry saved successfully",
      ),
    );
    setIsSubmitting(false);
    reset();
    toggleEditTimingsModal();
  };

  const handleCancel = () => {
    reset();
    setErrors({});
    toggleEditTimingsModal();
  };

  return (
    <Modal
      modalHeading={t("faultTracking.editTimings", "Edit Alarm Timings")}
      open={isEditTimingsModalOpen}
      onRequestClose={handleCancel}
      primaryButtonText={t("faultTracking.save", "Save")}
      secondaryButtonText={t("faultTracking.cancel", "Cancel")}
      onRequestSubmit={handleSubmit}
      onSecondarySubmit={handleCancel}
      passiveModal={false}
      primaryButtonDisabled={isSubmitting || !isValid}
    >
      <p style={{ marginBottom: "1.5rem" }}>
        {t(
          "faultTracking.editTimingsSubtitle",
          "Adjust the date range, start and end time, the duration will update automatically.",
        )}
      </p>
      {/* todo: add form */}
    </Modal>
  );
}
