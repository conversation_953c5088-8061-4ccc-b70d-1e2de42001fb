import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom/vitest";
import { EditTimingsModal } from "./edit-timings-modal";

// Mock the translation hook
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (_key: string, fallback: string) => fallback,
  }),
}));

// Mock the config hook
vi.mock("../../../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(),
}));

// Mock the notification hook
const mockSuccess = vi.fn();
vi.mock("../../../../components/toast/use-notification", () => ({
  useNotification: () => ({
    success: mockSuccess,
  }),
}));

// Mock the logger
vi.mock("../../../../utils/logger", () => ({
  Logger: vi.fn().mockImplementation(() => ({
    info: vi.fn(),
  })),
}));

// Mock the edit timings form hook
const mockFormHook = {
  formData: {
    startDate: new Date("2024-02-15"),
    endDate: new Date("2024-02-15"),
    startTime: "10:00",
    startPeriod: "AM" as const,
    endTime: "11:00",
    endPeriod: "AM" as const,
    startSeconds: "",
    endSeconds: "",
    comments: "Test comment",
  },
  reset: vi.fn(),
  validate: vi.fn(),
  setErrors: vi.fn(),
  isValid: true,
};

vi.mock("../../hooks/edit-timings/use-edit-timings-form", () => ({
  useEditTimingsForm: () => mockFormHook,
}));

// Mock the API formatter
vi.mock("../../utils/edit-timings-api-formatter", () => ({
  formatEditTimingsForAPI: vi.fn(),
}));

import { useConfigSetting } from "../../../../config/hooks/use-config";
import { formatEditTimingsForAPI } from "../../utils/edit-timings-api-formatter";

describe("EditTimingsModal", () => {
  const mockToggleModal = vi.fn();

  const defaultProps = {
    isEditTimingsModalOpen: true,
    toggleEditTimingsModal: mockToggleModal,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default config setting mock
    vi.mocked(useConfigSetting).mockReturnValue({
      setting: {
        id: "site-time-zone",
        name: "Site Time Zone",
        value: "America/New_York",
        dataType: "string",
      },
      isLoading: false,
      error: null,
    });

    // Setup default API formatter mock
    vi.mocked(formatEditTimingsForAPI).mockReturnValue({
      startDateLocal: "2024-02-15T15:00:00.000Z",
      endDateLocal: "2024-02-15T16:00:00.000Z",
      comments: "Test comment",
    });

    // Reset notification mock
    mockSuccess.mockClear();

    // Reset form hook mocks
    mockFormHook.validate.mockReturnValue({});
    mockFormHook.isValid = true;
  });

  describe("rendering", () => {
    it("should render the modal when open", () => {
      render(<EditTimingsModal {...defaultProps} />);

      expect(screen.getByText("Edit Alarm Timings")).toBeInTheDocument();
      expect(
        screen.getByText(
          "Adjust the date range, start and end time, the duration will update automatically.",
        ),
      ).toBeInTheDocument();
    });

    it("should respect the open prop", () => {
      const { rerender } = render(
        <EditTimingsModal {...defaultProps} isEditTimingsModalOpen={false} />,
      );

      // First render with closed - modal may exist but should not be interactable
      // Let's just verify we can control the open state

      // Now render with open
      rerender(
        <EditTimingsModal {...defaultProps} isEditTimingsModalOpen={true} />,
      );

      // Should be able to find interactive elements when open
      expect(screen.getByText("Edit Alarm Timings")).toBeInTheDocument();
      expect(screen.getByText("Save")).toBeInTheDocument();
      expect(screen.getByText("Cancel")).toBeInTheDocument();
    });

    it("should render save and cancel buttons", () => {
      render(<EditTimingsModal {...defaultProps} />);

      expect(screen.getByText("Save")).toBeInTheDocument();
      expect(screen.getByText("Cancel")).toBeInTheDocument();
    });

    it("should enable save button when form is valid", () => {
      mockFormHook.isValid = true;
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      expect(saveButton).not.toBeDisabled();
    });

    it("should disable save button when form is invalid", () => {
      mockFormHook.isValid = false;
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      expect(saveButton).toBeDisabled();
    });
  });

  describe("form submission", () => {
    it("should handle successful form submission", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(mockFormHook.validate).toHaveBeenCalled();
      expect(vi.mocked(formatEditTimingsForAPI)).toHaveBeenCalledWith(
        mockFormHook.formData,
        "America/New_York",
      );

      expect(mockSuccess).toHaveBeenCalledWith(
        "Manual entry saved successfully",
      );
      expect(mockFormHook.reset).toHaveBeenCalled();
      expect(mockToggleModal).toHaveBeenCalled();
    });

    it("should handle form validation errors", async () => {
      const user = userEvent.setup();
      const validationErrors = {
        startDate: "Start date is required",
        comments: "Comments are required",
      };

      mockFormHook.validate.mockReturnValue(validationErrors);
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(mockFormHook.validate).toHaveBeenCalled();
      expect(mockFormHook.setErrors).toHaveBeenCalledWith(validationErrors);
      expect(vi.mocked(formatEditTimingsForAPI)).not.toHaveBeenCalled();

      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockToggleModal).not.toHaveBeenCalled();
    });

    it("should use default timezone when config is not available", async () => {
      const user = userEvent.setup();
      vi.mocked(useConfigSetting).mockReturnValue({
        setting: undefined,
        isLoading: false,
        error: null,
      });

      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(vi.mocked(formatEditTimingsForAPI)).toHaveBeenCalledWith(
        mockFormHook.formData,
        "America/New_York",
      );
    });

    it("should clear errors on successful submission", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(mockFormHook.setErrors).toHaveBeenCalledWith({});
    });

    it("should disable save button during submission", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");

      // Start submission
      await user.click(saveButton);

      // Button should be disabled during submission
      // Note: In real implementation, we'd need to test the loading state
      // For now, we verify the submission process completes
      expect(mockFormHook.validate).toHaveBeenCalled();
    });
  });

  describe("modal interactions", () => {
    it("should handle cancel button click", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const cancelButton = screen.getByText("Cancel");
      await user.click(cancelButton);

      expect(mockFormHook.reset).toHaveBeenCalled();
      expect(mockFormHook.setErrors).toHaveBeenCalledWith({});
      expect(mockToggleModal).toHaveBeenCalled();
    });

    it("should handle modal close (X button)", () => {
      render(<EditTimingsModal {...defaultProps} />);

      // Simulate close event (onRequestClose)
      const modal = screen.getByRole("dialog");
      fireEvent.keyDown(modal, { key: "Escape" });

      // The actual close behavior depends on Carbon's Modal implementation
      // We can verify our handler would be called
      expect(mockFormHook.reset).toHaveBeenCalled();
      expect(mockFormHook.setErrors).toHaveBeenCalledWith({});
      expect(mockToggleModal).toHaveBeenCalled();
    });
  });

  describe("configuration handling", () => {
    it("should use configured timezone from settings", async () => {
      const user = userEvent.setup();
      vi.mocked(useConfigSetting).mockReturnValue({
        setting: {
          id: "site-time-zone",
          name: "Site Time Zone",
          value: "Europe/London",
          dataType: "string",
        },
        isLoading: false,
        error: null,
      });

      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(vi.mocked(formatEditTimingsForAPI)).toHaveBeenCalledWith(
        mockFormHook.formData,
        "Europe/London",
      );
    });

    it("should handle undefined timezone config", async () => {
      const user = userEvent.setup();
      vi.mocked(useConfigSetting).mockReturnValue({
        setting: {
          id: "site-time-zone",
          name: "Site Time Zone",
          value: undefined,
          dataType: "string",
        },
        isLoading: false,
        error: null,
      });

      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(vi.mocked(formatEditTimingsForAPI)).toHaveBeenCalledWith(
        mockFormHook.formData,
        "America/New_York",
      );
    });
  });

  describe("accessibility", () => {
    it("should have proper modal structure", () => {
      render(<EditTimingsModal {...defaultProps} />);

      const modal = screen.getByRole("dialog");
      expect(modal).toBeInTheDocument();
      expect(modal).toHaveAttribute("aria-modal", "true");
    });

    it("should have proper button roles", () => {
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByRole("button", { name: "Save" });
      const cancelButton = screen.getByRole("button", { name: "Cancel" });

      expect(saveButton).toBeInTheDocument();
      expect(cancelButton).toBeInTheDocument();
    });

    it("should properly disable save button when invalid", () => {
      mockFormHook.isValid = false;
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByRole("button", { name: "Save" });
      expect(saveButton).toBeDisabled();
    });
  });

  describe("form integration", () => {
    it("should call form validation on submit", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(mockFormHook.validate).toHaveBeenCalledTimes(1);
    });

    it("should reset form on cancel", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const cancelButton = screen.getByText("Cancel");
      await user.click(cancelButton);

      expect(mockFormHook.reset).toHaveBeenCalledTimes(1);
    });

    it("should reset form on successful submission", async () => {
      const user = userEvent.setup();
      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      expect(mockFormHook.reset).toHaveBeenCalledTimes(1);
    });

    it("should respect form validity state", () => {
      // Test valid form
      mockFormHook.isValid = true;
      const { rerender } = render(<EditTimingsModal {...defaultProps} />);

      let saveButton = screen.getByText("Save");
      expect(saveButton).not.toBeDisabled();

      // Test invalid form
      mockFormHook.isValid = false;
      rerender(<EditTimingsModal {...defaultProps} />);

      saveButton = screen.getByText("Save");
      expect(saveButton).toBeDisabled();
    });
  });

  describe("error handling", () => {
    it("should call validation before attempting API formatting", async () => {
      const user = userEvent.setup();

      render(<EditTimingsModal {...defaultProps} />);

      const saveButton = screen.getByText("Save");
      await user.click(saveButton);

      // Validation should be called first
      expect(mockFormHook.validate).toHaveBeenCalled();
      // API formatter should be called after validation passes
      expect(vi.mocked(formatEditTimingsForAPI)).toHaveBeenCalled();
    });
  });
});
