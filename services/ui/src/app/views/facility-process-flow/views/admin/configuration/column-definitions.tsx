import { createColumnHelper } from "@tanstack/react-table";
import { Button } from "@carbon/react";
import { FactConfigurationData, MetricConfigFact } from "./types";
import { CheckmarkOutline, CloseOutline } from "@carbon/icons-react";

type FactColumnHelper = ReturnType<
  typeof createColumnHelper<FactConfigurationData>
>;

export const getFactDatagridColumns = (
  columnHelper: FactColumnHelper | undefined,
  handleDelete: (itemName: string) => void,
  handleView: (itemName: string) => void,
  // TODO: Replace this any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): any[] => {
  if (!columnHelper) return [];

  return [
    columnHelper.accessor("fact_name", {
      header: "Fact Name",
      id: "fact_name",
      size: 200,
    }),
    columnHelper.accessor("enabled", {
      header: "Enabled",
      size: 100,
    }),
    columnHelper.accessor("active", {
      header: "Active",
      size: 100,
    }),
    columnHelper.accessor("num_configurations", {
      header: "Configurations",
      size: 100,
    }),
    columnHelper.accessor("fact_name", {
      header: "",
      id: "deleteButton",
      size: 50,
      cell: ({ getValue }) => {
        const itemName = getValue();
        if (!itemName) return <div>--</div>;
        return (
          <div>
            <Button
              className="gridButton"
              kind="tertiary"
              size="sm"
              onClick={() => handleDelete(itemName)}
            >
              Delete
            </Button>
          </div>
        );
      },
    }),
    columnHelper.accessor("fact_name", {
      header: "",
      id: "viewButton",
      size: 50,
      cell: ({ getValue }) => {
        const itemName = getValue();
        if (!itemName) return <div>--</div>;
        return (
          <Button
            className="gridButton"
            kind="tertiary"
            size="sm"
            onClick={() => handleView(itemName)}
          >
            View
          </Button>
        );
      },
    }),
  ];
};

type MetricConfigFactColumnHelper = ReturnType<
  typeof createColumnHelper<MetricConfigFact>
>;

export const getMetricConfigFactDatagridColumns = (
  columnHelper: MetricConfigFactColumnHelper | undefined,
  handleDelete: (factType: string) => void,
  handleView: (factType: string) => void,
  // TODO: Replace this any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): any[] => {
  if (!columnHelper) return [];

  return [
    columnHelper.accessor("factType", {
      header: "Fact Name",
      id: "factType",
      size: 200,
    }),
    columnHelper.accessor("totalConfigs", {
      header: "Total Configs",
      id: "totalConfigs",
      size: 120,
    }),
    columnHelper.accessor("enabledConfigs", {
      header: "Enabled Configs",
      id: "enabledConfigs",
      size: 140,
    }),
    columnHelper.accessor("active", {
      header: "Active",
      id: "active",
      size: 100,
      cell: ({ getValue }) => {
        const active = getValue();
        return active ? (
          <CheckmarkOutline color="green" />
        ) : (
          <CloseOutline color="red" />
        );
      },
    }),
    columnHelper.accessor("factType", {
      header: "",
      id: "deleteButton",
      size: 50,
      cell: ({ getValue }) => {
        const factType = getValue();
        if (!factType) return <div>--</div>;
        return (
          <div>
            <Button
              className="gridButton"
              kind="tertiary"
              size="sm"
              onClick={() => handleDelete(factType)}
            >
              Delete
            </Button>
          </div>
        );
      },
    }),
    columnHelper.accessor("factType", {
      header: "",
      id: "viewButton",
      size: 50,
      cell: ({ getValue }) => {
        const factType = getValue();
        if (!factType) return <div>--</div>;
        return (
          <Button
            className="gridButton"
            kind="tertiary"
            size="sm"
            onClick={() => handleView(factType)}
          >
            View
          </Button>
        );
      },
    }),
  ];
};
