import {
  ContextService,
  DatabaseProvider,
  DiService,
  EnvironmentService,
  FacilityUtils,
  IctError,
  Metric,
  RedisClient,
  WinstonLogger,
} from 'ict-api-foundations';

import {
  Edge,
  GRAPH_AREA_CACHE_NAME,
  GRAPH_CACHE_NAME,
  GRAPH_EDGE_CACHE_NAME,
  GRAPH_METRIC_CACHE_NAME,
  PROCESSED_TIME_CACHE_NAME,
  ProcessFlowGraphResponse,
  UpdatedArea,
  UpdatePositionPayload,
} from '../defs/inventory-process-flow-def.ts';

/**
 * TTL in seconds for the Neo4J auth key retrieval and cache
 */
const REDIS_CACHE_TTL = 300;

@DiService()
export class ProcessFlowStore {
  constructor(
    private context: ContextService,
    private redisClient: RedisClient,
    private logger: WinstonLogger,
    private envService: EnvironmentService,
  ) {}

  get dbProvider(): DatabaseProvider {
    return this.context.dbProvider;
  }

  /**
   * Fetches the process flow graph from Neo4j, using Redis as a cache.
   * Returns all nodes (areas, aisles, levels, shuttles) and their metrics for a given view.
   * If cached data exists, it is returned; otherwise, the query is executed on Neo4j.
   * @param view The view to fetch nodes from (defaults to 'facility')
   * @returns ProcessFlowGraphResponse containing all nodes and their connections with metrics
   */
  async getAreaGraph(view = 'facility'): Promise<ProcessFlowGraphResponse> {
    const neo4jDb = this.dbProvider.neo4j;
    const session = neo4jDb.getDriver().session();
    const tenantId = FacilityUtils.resolveTenantId(this.context, this.logger);
    const facilityId = FacilityUtils.resolveFacilityId(
      this.context,
      this.logger,
    );
    const cacheKey = `${tenantId}:${facilityId}:${view}:${GRAPH_CACHE_NAME}`;

    const query = `
      // Match all relevant nodes that include the current view
      MATCH (a)
      WHERE $view IN a.views
        AND (a:Area OR a:Aisle OR a:Lift OR a:Station OR a:StationLocation)

      // Match levels and shuttles underneath aisles
      OPTIONAL MATCH (a:Aisle)-[:CONTAINS]->(level:Level)
      OPTIONAL MATCH (level)-[:CONTAINS]->(shuttle:Shuttle)
      OPTIONAL MATCH (shuttle)-[sr:HAS_METRIC]->(shuttleMetric:Metric)
      WHERE $view IN sr.views

      // Collect shuttle metrics first to avoid nested aggregation
      WITH a, level, shuttle, 
          COLLECT(DISTINCT shuttleMetric.name) AS shuttleMetricNames

      WITH a, level, shuttle, 
          {
            id: elementId(shuttle),
            name: shuttle.name,
            metrics: shuttleMetricNames
          } AS shuttleObj

      // Collect shuttles grouped under their levels
      WITH a, level, 
          COLLECT(DISTINCT shuttleObj) AS shuttles

      WITH a, 
          COLLECT(
            CASE WHEN level IS NOT NULL THEN {
              id: elementId(level),
              label: level.name,
              shuttles: [s IN shuttles WHERE s.id IS NOT NULL]
            } END
          ) AS aisleLevels

      // Collect metrics directly connected to nodes
      OPTIONAL MATCH (a)-[mr:HAS_METRIC]->(metric:Metric)
      WHERE $view IN mr.views

      WITH a, aisleLevels, COLLECT(DISTINCT metric.name) AS nodeMetrics

      WITH a,
          CASE 
            WHEN SIZE(aisleLevels) = 1 AND aisleLevels[0] IS NULL THEN [] 
            ELSE aisleLevels 
          END AS aisleLevels,
          nodeMetrics

      // Build the area node structure
      WITH COLLECT({
        id: elementId(a),
        label: a.name,
        nodeType: labels(a)[0],
        metrics: nodeMetrics,
        aisleLevels: aisleLevels,
        position: {
          x: CASE 
            WHEN $view = 'facility' THEN COALESCE(a.x, 0)
            ELSE COALESCE(a[$sanitizedView + '_x'], a.x, 0)
          END,
          y: CASE 
            WHEN $view = 'facility' THEN COALESCE(a.y, 0)
            ELSE COALESCE(a[$sanitizedView + '_y'], a.y, 0)
          END
        }
      }) AS areas

      // Find edges between nodes in the same view
      OPTIONAL MATCH (source)-[r:CONNECTS_TO]->(target)
      WHERE $view IN source.views AND $view IN target.views
        AND (source:Area OR source:Aisle OR source:Lift OR source:Station OR source:StationLocation)
        AND (target:Area OR target:Aisle OR target:Lift OR target:Station OR target:StationLocation)

      WITH areas, COLLECT({
        id: elementId(r),
        source: elementId(source),
        target: elementId(target),
        direction: r.direction,
        metrics: CASE 
          WHEN r.metrics IS NOT NULL THEN r.metrics
          ELSE []
        END
      }) AS edges

      RETURN {
        areas: [a IN areas WHERE a.id IS NOT NULL],
        edges: [e IN edges WHERE e.id IS NOT NULL]
      }
    `;

    try {
      const cachedData = await this.redisClient.get(cacheKey);
      if (cachedData && !this.envService.redis.localRequestCacheOverride) {
        const ttl = await this.redisClient.ttl(cacheKey);
        this.logger.info('Process Flow graph cache hit', {
          cacheKey,
          ttl,
          cachedData,
        });
        const cachedGraph = JSON.parse(cachedData);
        return cachedGraph;
      }

      this.logger.info('Executing Cypher query to fetch all Area nodes...', {
        view,
      });
      const result = await session.run(query, {
        view,
        sanitizedView: view.replace(/-/g, '_'),
      });
      this.logger.info('Cypher query executed successfully.');

      if (result.records.length > 0) {
        const {areas, edges} = result.records[0].get(0);
        const results = {areas, edges};

        // Add detailed logging of the results
        this.logger.info('Process Flow graph results:', {
          results,
        });

        this.redisClient.setEx(
          cacheKey,
          REDIS_CACHE_TTL,
          JSON.stringify(results),
        );

        return results;
      }

      throw IctError.noContent(); // Return an no content response if no records
    } catch (error) {
      this.logger.error('Failed to execute Neo4j query', {error, view});
      if (error instanceof IctError) {
        throw error;
      }
      throw IctError.internalServerError(
        'Failed to retrieve process flow areas',
        error,
      );
    } finally {
      await session.close();
      this.logger.info('Neo4j session closed.');
    }
  }

  /**
   * Retrieves metrics for a given node or relationship in the process flow graph.
   * * @param id The id of the node or relationship to fetch metrics for
   * * @param view The view to filter metrics by (optional)
   */
  async getElement(id: string, view?: string): Promise<{metrics: string[]}> {
    const neo4jDb = this.dbProvider.neo4j;
    const session = neo4jDb.getDriver().session();

    // Decode the ID in case it's URL-encoded
    const decodedId = decodeURIComponent(id);

    this.logger.info('getElement called with ID:', {
      id,
      decodedId,
      idType: typeof id,
      idLength: id?.length,
      idIsEmpty: !id,
      idIsNull: id === null,
      idIsUndefined: id === undefined,
    });

    // If no ID is provided, return empty metrics
    if (!id) {
      this.logger.info('No ID provided, returning empty metrics array');
      return {metrics: []};
    }

    // Query to find the element by its ID
    const query = `
      // Case 1: It's a node with HAS_METRIC relationships
      MATCH (n)
      WHERE elementId(n) = $id 
      OPTIONAL MATCH (n)-[r:HAS_METRIC]->(metric:Metric)
      ${view ? 'WHERE $view IN r.views' : ''}
      RETURN {
        metrics: COLLECT(metric.name)
      } AS result

      UNION

      // Case 2: It's an edge with metrics property
      MATCH (a)-[r:CONNECTS_TO]->(b)
      WHERE elementId(r) = $id OR r.id = $id
      RETURN {
        metrics: CASE 
          WHEN r.metrics IS NOT NULL THEN r.metrics
          ELSE []
        END
      } AS result
    `;

    try {
      this.logger.info('Executing Cypher query to fetch element', {
        id: decodedId,
      });
      const result = await session.run(query, {
        id: decodedId,
        ...(view && {view}),
      });

      this.logger.info('Cypher query executed successfully.', {
        recordCount: result.records.length,
        records: result.records.map(record => record.toObject()),
      });

      if (result.records.length > 0) {
        // Take the first non-empty metrics array
        const area = result.records
          .find(record => {
            const metrics = record.get('result').metrics;
            return metrics && metrics.length > 0;
          })
          ?.get('result') || {metrics: []};

        return area;
      }

      throw IctError.noContent();
    } catch (error) {
      if (error instanceof IctError) {
        throw error;
      }
      throw IctError.internalServerError('Failed to retrieve element', error);
    } finally {
      await session.close();
      this.logger.info('Neo4j session closed.');
    }
  }

  // find the counterpart for an edge
  // bidirectional counterpart will have it's source be edge's targert, and it's target be edge's source
  // source->target and target->source
  async getBiDirectionalEdge(id: string): Promise<{metrics: string[]}> {
    const neo4jDb = this.dbProvider.neo4j;
    const session = neo4jDb.getDriver().session();

    const query = `
    MATCH (a)-[r1:CONNECTS_TO]->(b), (b)-[r2:CONNECTS_TO]->(a)
    WHERE elementId(r1) = $id
    AND r1 <> r2
    AND type(r1) = type(r2)
    RETURN {
        metrics: r2.metrics,
        node: b
    } AS result
    `;
    try {
      this.logger.info(
        'Executing Cypher query to see if edge is bidirectional and return that edge',
        {id},
      );
      const result = await session.run(query, {
        id,
      });

      this.logger.info('Cypher query executed successfully.');

      if (result.records.length > 0) {
        const area = result.records[0].get('result');
        return area;
        // if no bi edge found, return nothing - an edge can be non bidirectional
      }
      return {metrics: []};
    } catch (error) {
      if (error instanceof IctError) {
        throw error;
      }
      throw IctError.internalServerError('Failed to retrieve element', error);
    } finally {
      await session.close();
      this.logger.info('Neo4j session closed.');
    }
  }

  /**
   *
   * Retrieves metric nodes from Neo4j based on a list of metric names.
   */
  async getMetricNodes(metrics: string[]): Promise<Metric[]> {
    const neo4jDb = this.dbProvider.neo4j;
    const session = neo4jDb.getDriver().session();

    this.logger.info('getMetricNodes called with metrics:', {
      metrics,
      metricsLength: metrics?.length,
      metricsIsEmpty: !metrics || metrics.length === 0,
    });

    // If no metrics are provided, return an empty array
    if (!metrics || metrics.length === 0) {
      this.logger.info('No metrics provided, returning empty array');
      return [];
    }

    const query = `
      // First try to find metric nodes
      MATCH (m:Metric)
      WHERE m.name IN $metrics
      RETURN m

      UNION

      // Then try to find metrics on CONNECTS_TO relationships
      MATCH ()-[r:CONNECTS_TO]->()
      WHERE r.name IN $metrics
      RETURN {
        properties: {
          name: r.name,
          units: r.units,
          id: r.name,
          description: r.description
        }
      } as m
    `;

    try {
      this.logger.info('Executing Cypher query to fetch metrics...');
      const result = await session.run(query, {metrics});
      this.logger.info('Cypher query executed successfully.');

      if (result.records.length > 0) {
        return result.records.map(record => {
          const metricNode = record.get('m');
          if (metricNode.properties) {
            // Handle Neo4j nodes - return all properties including description
            const metricProperties = metricNode.properties;
            return {
              id: metricProperties.name,
              description: metricProperties.description,
              units: metricProperties.units,
              // Include any other properties that might exist
              ...metricProperties,
            };
          }
          // Handle edge metrics
          return {
            id: metricNode.name,
            type: 'edge',
            description: metricNode.description,
            units: metricNode.units,
          };
        });
      }

      throw IctError.noContent();
    } catch (error) {
      if (error instanceof IctError) {
        throw error;
      }
      throw IctError.internalServerError(
        'Failed to retrieve process flow areas',
        error,
      );
    } finally {
      await session.close();
      this.logger.info('Neo4j session closed.');
    }
  }

  /**
   * Updates the position of an area node in Neo4j and invalidates the relevant Redis cache.
   */
  async updatePosition(
    payload: UpdatePositionPayload,
    view = 'facility',
  ): Promise<UpdatedArea> {
    const neo4jDb = this.dbProvider.neo4j;
    const session = neo4jDb.getDriver().session();
    const tenantId = FacilityUtils.resolveTenantId(this.context, this.logger);
    const facilityId = FacilityUtils.resolveFacilityId(
      this.context,
      this.logger,
    );
    const cacheKey = `${tenantId}:${facilityId}:${view}:${GRAPH_CACHE_NAME}`;

    // Invalidate cache since we are updating an area
    await this.redisClient.clearKeys(cacheKey);

    // Ensure view is not empty
    const viewParam = view || 'facility';

    const query = `
      MATCH (a)
      WHERE elementId(a) = $id
      SET a.x = CASE 
        WHEN $view = 'facility' THEN $x
        ELSE a.x
      END,
      a.y = CASE 
        WHEN $view = 'facility' THEN $y
        ELSE a.y
      END,
      a.${viewParam.replace(/-/g, '_')}_x = CASE 
        WHEN $view <> 'facility' THEN $x
        ELSE a.${viewParam.replace(/-/g, '_')}_x
      END,
      a.${viewParam.replace(/-/g, '_')}_y = CASE 
        WHEN $view <> 'facility' THEN $y
        ELSE a.${viewParam.replace(/-/g, '_')}_y
      END

      RETURN a
    `;

    try {
      this.logger.info('Executing query to update node', {
        payload,
        view: viewParam,
      });
      const result = await session.run(query, {
        id: payload.id,
        x: payload.position.x,
        y: payload.position.y,
        view: viewParam,
      });

      if (result.records.length > 0) {
        return result.records[0].get('a');
      }

      throw IctError.noContent(); // Return an no content response if no records
    } catch (error) {
      if (error instanceof IctError) {
        throw error;
      }
      throw IctError.internalServerError(
        'Failed to update process flow area',
        error,
      );
    } finally {
      await session.close();
    }
  }

  /**
   * Updates the direction of an edge
   * and invalidates the relevant Redis cache.
   *
   */
  async updateEdge(edge: Edge): Promise<Edge> {
    const neo4jDb = this.dbProvider.neo4j;
    const session = neo4jDb.getDriver().session();
    const tenantId = FacilityUtils.resolveTenantId(this.context, this.logger);
    const facilityId = FacilityUtils.resolveFacilityId(
      this.context,
      this.logger,
    );
    const cacheKey = `${tenantId}:${facilityId}:*:${GRAPH_CACHE_NAME}`;

    // Invalidate cache since we are updating an edge
    await this.redisClient.clearKeys(cacheKey);

    const query = `
      MATCH (a)-[r:CONNECTS_TO]->(b)
      WHERE elementId(r) = $id
      SET r.direction = $newDirection
      RETURN r
      `;

    try {
      this.logger.info('Executing query to update edge', {edgeId: edge.id});
      const result = await session.run(query, {
        id: edge.id,
        newDirection: edge.direction,
      });
      if (result.records.length > 0) {
        return edge;
      }

      throw IctError.noContent();
    } catch (error) {
      if (error instanceof IctError) {
        throw error;
      }
      throw IctError.internalServerError(
        'Failed to update process flow edge',
        error,
      );
    } finally {
      await session.close();
    }
  }

  /**
   * Updates the panel group attribute of a metric node in Neo4j.
   */
  async updateMetric(metric: Metric): Promise<Metric> {
    const neo4jDb = this.dbProvider.neo4j;
    const session = neo4jDb.getDriver().session();

    const query = `
      MATCH (a:Metric)
      WHERE a.name = $name
      SET a.panelGroup = $panelGroup
      RETURN a
    `;

    try {
      this.logger.info('Executing query to update node', {areaId: metric.id});
      const result = await session.run(query, {
        name: metric.id,
        panelGroup: metric.panelGroup ?? '',
      });
      if (result.records.length > 0) {
        return metric;
      }

      throw IctError.noContent(); // Return an no content response if no records
    } catch (error) {
      if (error instanceof IctError) {
        throw error;
      }
      throw IctError.internalServerError(
        'Failed to update process flow area',
        error,
      );
    } finally {
      await session.close();
    }
  }

  /**
   * Updates the panel group attribute of a metric node in Neo4j.
   */
  async deleteGraph() {
    const neo4jDb = this.dbProvider.neo4j;
    const session = neo4jDb.getDriver().session();

    const query = `
      MATCH (n)
      detach
      delete n
    `;

    try {
      this.logger.info('Executing query to delete all nodes and edges');
      const result = await session.run(query);

      return result.records.length;
    } catch (error) {
      if (error instanceof IctError) {
        throw error;
      }
      throw IctError.internalServerError(
        'Failed to delete nodes and edges',
        error,
      );
    } finally {
      await session.close();
    }
  }

  /**
   * Removes Redis cache keys based on the provided scope
   * When scope is graph, we delete GRAPH_AREA_CACHE_NAME, GRAPH_EDGE_CACHE_NAME, GRAPH_METRIC_CACHE_NAME, GRAPH_CACHE_NAME
   * When scope is metrics, we delete everything EXCEPT GRAPH_AREA_CACHE_NAME, GRAPH_EDGE_CACHE_NAME, GRAPH_METRIC_CACHE_NAME, PROCESSED_TIME_CACHE_NAME
   * @param scope - all, metrics, or graph.
   */
  async clearRedisMetricsCache(scope: string) {
    const tenantId = FacilityUtils.resolveTenantId(this.context, this.logger);
    const facilityId = FacilityUtils.resolveFacilityId(
      this.context,
      this.logger,
    );

    const graphAreaCacheName = `${tenantId}:${facilityId}:${GRAPH_AREA_CACHE_NAME}`;
    const graphEdgeCacheName = `${tenantId}:${facilityId}:${GRAPH_EDGE_CACHE_NAME}`;
    const graphMetricCacheName = `${tenantId}:${facilityId}:${GRAPH_METRIC_CACHE_NAME}`;
    const graphCachePattern = `${tenantId}:${facilityId}:*:${GRAPH_CACHE_NAME}`;

    if (scope === 'graph' || scope === 'all') {
      // there is a cache for each view, so we need to get all of them
      const graphCacheNames = await this.redisClient.keys(graphCachePattern);
      const deletedCount = await this.redisClient.unlink([
        graphAreaCacheName,
        graphEdgeCacheName,
        graphMetricCacheName,
        ...graphCacheNames,
      ]);
      this.logger.info(`Unlinked ${deletedCount} graph cache keys`);
    }

    if (scope === 'metrics' || scope === 'all') {
      // get all keys for the tenant EXCEPT certain keys, then clear those
      const keys = await this.redisClient.keys(`${tenantId}:${facilityId}:*`);
      const keysToExclude = [
        GRAPH_AREA_CACHE_NAME,
        GRAPH_EDGE_CACHE_NAME,
        GRAPH_METRIC_CACHE_NAME,
        PROCESSED_TIME_CACHE_NAME,
      ];

      // Filter out keys that contain any of the excluded cache names
      const keysToClear = keys.filter(key => {
        return !keysToExclude.some(excludePattern =>
          key.includes(excludePattern),
        );
      });

      // Clear all filtered keys in a single batch operation
      if (keysToClear.length > 0) {
        const deletedCount = await this.redisClient.unlink(keysToClear);
        this.logger.info(`Unlinked ${deletedCount} metric cache keys`);
      }
    }
  }
}
