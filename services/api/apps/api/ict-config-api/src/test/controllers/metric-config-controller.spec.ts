import 'reflect-metadata';
import sinon from 'sinon';
import request from 'supertest';
import {
  Container,
  ContextService,
  appSetup,
  ApiMiddleware,
  EnvironmentService,
  Environment,
  ConfigStore,
  AppConfigSettingSource,
  IctError,
  SecurityRoles,
} from 'ict-api-foundations';
import {EntityTypes, CustomMetricConfigurationEntity} from 'ict-api-schema';
import {expect} from 'chai';
import {MetricConfigService} from '../../services/metric-config-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import type {
  MetricConfigDetail,
  MetricConfigValueResponse,
} from '../../defs/metric-config-detail.ts';
import {MetricConfigFact} from '../../defs/metric-config-facts.ts';

describe('MetricConfigController', () => {
  const app = appSetup(RegisterRoutes);
  let metricConfigServiceStub: sinon.SinonStubbedInstance<MetricConfigService>;
  let contextServiceRolesStub: sinon.SinonStub;

  before(async () => {
    // Mock environment service to return local environment
    const envService = Container.get(EnvironmentService);
    sinon.stub(envService, 'app').get(() => ({
      env: Environment.local,
      authDomain: 'test-domain',
      authAudience: 'test-audience',
      unitTest: 'true',
    }));

    // Mock ConfigStore to return facility maps
    const configStore = Container.get(ConfigStore);
    sinon.stub(configStore, 'findSpecificSettingValue').resolves({
      id: 'facility-maps',
      name: 'facility-maps',
      group: 'facility',
      dataType: 'json',
      value: [
        {
          id: 'test-facility',
          name: 'Test Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ],
      source: AppConfigSettingSource.default,
    });

    // Apply all middlewares
    await ApiMiddleware.applyApiDefaultMiddlewares(app);
  });

  beforeEach(() => {
    metricConfigServiceStub = sinon.createStubInstance(MetricConfigService);
    Container.set(MetricConfigService, metricConfigServiceStub);

    // Mock context service roles
    const contextService = Container.get(ContextService);
    contextServiceRolesStub = sinon.stub(contextService, 'userRoles');
    contextServiceRolesStub.value([SecurityRoles.CT_ENGINEERS]);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getMetricConfigSummaries route', () => {
    const url = '/config/process-flow/metric-configs';
    const queryParams = {
      metric_name: 'test-metric',
      config_type: 'node',
      metric_id: undefined,
      fact_type: undefined,
      node_name: undefined,
      active: undefined,
      enabled: undefined,
    };

    it('should return 404 when no default facility exists', async () => {
      // Mock context service to return no default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => []);

      // Stub the metric config service to throw facility error
      metricConfigServiceStub.getMetricConfigSummaries.rejects(
        IctError.notFound(
          'No default facility found in facility maps. Please ensure a default facility is configured.',
        ),
      );

      const response = await request(app).get(url).query(queryParams);

      expect(response.status).to.equal(IctError.notFound().statusCode);
      expect(response.body).to.have.property(
        'detail',
        'No default facility found in facility maps. Please ensure a default facility is configured.',
      );
      expect(response.headers['content-type']).to.match(/json/);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigSummaries);
    });

    it('should return 404 when no metric configs are found', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'default',
          name: 'Default Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      metricConfigServiceStub.getMetricConfigSummaries.resolves([]);

      const response = await request(app).get(url).query(queryParams);

      expect(response.status).to.equal(IctError.notFound().statusCode);
      expect(response.body)
        .to.have.property('detail')
        .that.includes('No metric configurations found with filters');
      expect(response.headers['content-type']).to.match(/json/);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigSummaries);
      sinon.assert.calledWith(
        metricConfigServiceStub.getMetricConfigSummaries,
        {
          metricName: queryParams.metric_name,
          metricId: queryParams.metric_id,
          factType: queryParams.fact_type,
          nodeName: queryParams.node_name,
          active: queryParams.active,
          enabled: queryParams.enabled,
          configType: queryParams.config_type,
        },
      );
    });

    it('should return metric configs when found', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'default',
          name: 'Default Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      const mockConfig = {
        id: 'test-config',
        metricName: 'test-metric',
        configType: 'node',
        value: {some: 'value'},
        isCustom: false,
        facilityId: 'default',
        views: ['facility'],
      };
      metricConfigServiceStub.getMetricConfigSummaries.resolves([mockConfig]);

      const response = await request(app).get(url).query(queryParams);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal([mockConfig]);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigSummaries);
      sinon.assert.calledWith(
        metricConfigServiceStub.getMetricConfigSummaries,
        {
          metricName: queryParams.metric_name,
          metricId: queryParams.metric_id,
          factType: queryParams.fact_type,
          nodeName: queryParams.node_name,
          active: queryParams.active,
          enabled: queryParams.enabled,
          configType: queryParams.config_type,
        },
      );
    });

    it('should handle service errors', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'default',
          name: 'Default Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      metricConfigServiceStub.getMetricConfigSummaries.rejects(
        IctError.internalServerError('Service error'),
      );

      const response = await request(app).get(url).query(queryParams);

      expect(response.status).to.equal(500);
      expect(response.body).to.have.property('title', 'Internal Server Error');
      expect(response.headers['content-type']).to.match(/json/);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigSummaries);
      sinon.assert.calledWith(
        metricConfigServiceStub.getMetricConfigSummaries,
        {
          metricName: queryParams.metric_name,
          metricId: queryParams.metric_id,
          factType: queryParams.fact_type,
          nodeName: queryParams.node_name,
          active: queryParams.active,
          enabled: queryParams.enabled,
          configType: queryParams.config_type,
        },
      );
    });
  });

  describe('putMetricConfig route', () => {
    const url = '/config/process-flow/metric-config';

    it('should create a new metric configuration and return 201', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon
        .stub(contextService, 'selectedFacilityId')
        .get(() => 'test-facility');
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'test-facility',
          name: 'Test Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const mockResult = {
        config: new CustomMetricConfigurationEntity(),
        isNew: true,
      };
      Object.assign(mockResult.config, {
        id: 'new-config-id',
        facilityId: 'test-facility',
        ...input,
      });

      metricConfigServiceStub.updateOrCreateMetricConfig.resolves(mockResult);

      const response = await request(app)
        .put(url)
        .send(input)
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(201);
      expect(response.body).to.have.property('id', 'new-config-id');
      expect(response.body).to.have.property('metricConfigName', 'test-metric');
      expect(response.body).to.have.property('configType', 'node');
      expect(response.body).to.have.property('isCustom', true);
      expect(response.body).to.have.property('facilityId', 'test-facility');
      sinon.assert.calledOnce(
        metricConfigServiceStub.updateOrCreateMetricConfig,
      );
      sinon.assert.calledWith(
        metricConfigServiceStub.updateOrCreateMetricConfig,
        input,
      );
    });

    it('should update an existing metric configuration and return 200', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon
        .stub(contextService, 'selectedFacilityId')
        .get(() => 'test-facility');
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'test-facility',
          name: 'Test Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      const input: MetricConfigDetail = {
        metricConfigName: 'existing-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Existing Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const mockResult = {
        config: new CustomMetricConfigurationEntity(),
        isNew: false,
      };
      Object.assign(mockResult.config, {
        id: 'existing-config-id',
        facilityId: 'test-facility',
        ...input,
      });

      metricConfigServiceStub.updateOrCreateMetricConfig.resolves(mockResult);

      const response = await request(app)
        .put(url)
        .send(input)
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(200);
      expect(response.body).to.have.property('id', 'existing-config-id');
      expect(response.body).to.have.property(
        'metricConfigName',
        'existing-metric',
      );
      expect(response.body).to.have.property('configType', 'node');
      expect(response.body).to.have.property('isCustom', true);
      expect(response.body).to.have.property('facilityId', 'test-facility');
      sinon.assert.calledOnce(
        metricConfigServiceStub.updateOrCreateMetricConfig,
      );
      sinon.assert.calledWith(
        metricConfigServiceStub.updateOrCreateMetricConfig,
        input,
      );
    });

    it('should handle service errors gracefully', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon
        .stub(contextService, 'selectedFacilityId')
        .get(() => 'test-facility');
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'test-facility',
          name: 'Test Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      metricConfigServiceStub.updateOrCreateMetricConfig.rejects(
        IctError.internalServerError('Database connection failed'),
      );

      const response = await request(app)
        .put(url)
        .send(input)
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(500);
      expect(response.body).to.have.property('title', 'Internal Server Error');
      sinon.assert.calledOnce(
        metricConfigServiceStub.updateOrCreateMetricConfig,
      );
    });
  });

  describe('getMetricConfigDetail route', () => {
    const metricName = 'test-metric';
    const url = `/config/process-flow/metric-config/${metricName}`;

    it('should return 200 with both default and custom configurations', async () => {
      const mockResponse: MetricConfigValueResponse = {
        default: {
          metricConfigName: metricName,
          configType: 'node' as const,
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: {default: true},
          active: {default: true},
          isCustom: false,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        },
        custom: {
          metricConfigName: metricName,
          configType: 'node' as const,
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric Custom',
          enabled: true,
          active: true,
          isCustom: true,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        },
      };

      metricConfigServiceStub.getMetricConfigDetail.resolves(mockResponse);

      const response = await request(app)
        .get(url)
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(200);
      expect(response.body.default).to.deep.equal(mockResponse.default);
      expect(response.body.custom).to.deep.equal(mockResponse.custom);
      expect(
        metricConfigServiceStub.getMetricConfigDetail.calledWith(
          metricName,
          undefined,
        ),
      ).to.be.true;
    });

    it('should return 200 with only default configuration when config_type=default', async () => {
      const mockResponse: MetricConfigValueResponse = {
        default: {
          metricConfigName: metricName,
          configType: 'node' as const,
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: {default: true},
          active: {default: true},
          isCustom: false,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        },
        custom: undefined,
      };

      metricConfigServiceStub.getMetricConfigDetail.resolves(mockResponse);

      const response = await request(app)
        .get(`${url}?config_type=default`)
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(200);
      expect(response.body.default).to.deep.equal(mockResponse.default);
      expect(response.body.custom).to.be.undefined;
      expect(
        metricConfigServiceStub.getMetricConfigDetail.calledWith(
          metricName,
          'default',
        ),
      ).to.be.true;
    });

    it('should return 200 with only custom configuration when config_type=custom', async () => {
      const mockResponse: MetricConfigValueResponse = {
        default: undefined,
        custom: {
          metricConfigName: metricName,
          configType: 'node' as const,
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric Custom',
          enabled: true,
          active: true,
          isCustom: true,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        },
      };

      metricConfigServiceStub.getMetricConfigDetail.resolves(mockResponse);

      const response = await request(app)
        .get(`${url}?config_type=custom`)
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(200);
      expect(response.body.default).to.be.undefined;
      expect(response.body.custom).to.deep.equal(mockResponse.custom);
      expect(
        metricConfigServiceStub.getMetricConfigDetail.calledWith(
          metricName,
          'custom',
        ),
      ).to.be.true;
    });

    it('should return 204 No Content when no configurations are found', async () => {
      const mockResponse: MetricConfigValueResponse = {
        default: undefined,
        custom: undefined,
      };

      metricConfigServiceStub.getMetricConfigDetail.resolves(mockResponse);

      const response = await request(app).get(url);

      expect(response.status).to.equal(204);
      expect(response.body).to.deep.equal({});
    });

    it('should return 500 when service throws an error', async () => {
      metricConfigServiceStub.getMetricConfigDetail.rejects(
        IctError.internalServerError('Database connection failed'),
      );

      const response = await request(app)
        .get(url)
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(500);
      expect(response.body).to.have.property('title', 'Internal Server Error');
    });
  });

  describe('getMetricConfigFacts route', () => {
    const url = '/config/process-flow/metric-configs/facts';

    it('should return metric configuration facts successfully', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'default',
          name: 'Default Facility',
          dataset: 'test-dataset',
          facilityId: 'test-facility-id',
          tenantId: 'test-tenant-id',
          default: true,
        },
      ]);

      const mockFacts: MetricConfigFact[] = [
        {
          factType: 'inventory',
          totalConfigs: 5,
          enabledConfigs: 3,
          active: true,
        },
        {
          factType: 'shipping',
          totalConfigs: 2,
          enabledConfigs: 1,
          active: false,
        },
      ];

      metricConfigServiceStub.getMetricConfigFacts.resolves(mockFacts);

      const response = await request(app)
        .get(url)
        .set('ict-facility-id', 'test-facility-id')
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockFacts);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigFacts);
    });

    it('should return empty array when no facts are found', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'default',
          name: 'Default Facility',
          dataset: 'test-dataset',
          facilityId: 'test-facility-id',
          tenantId: 'test-tenant-id',
          default: true,
        },
      ]);

      const mockFacts: MetricConfigFact[] = [];

      metricConfigServiceStub.getMetricConfigFacts.resolves(mockFacts);

      const response = await request(app)
        .get(url)
        .set('ict-facility-id', 'test-facility-id')
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('array').that.is.empty;
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigFacts);
    });

    it('should handle service errors gracefully', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'default',
          name: 'Default Facility',
          dataset: 'test-dataset',
          facilityId: 'test-facility-id',
          tenantId: 'test-tenant-id',
          default: true,
        },
      ]);

      metricConfigServiceStub.getMetricConfigFacts.rejects(
        new Error('Database connection failed'),
      );

      const response = await request(app)
        .get(url)
        .set('ict-facility-id', 'test-facility-id')
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(500);
      expect(response.body).to.have.property('title', 'Internal Server Error');
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigFacts);
    });

    it('should handle missing facility ID gracefully', async () => {
      // Mock context service to not have any facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => []);

      metricConfigServiceStub.getMetricConfigFacts.rejects(
        IctError.notFound(
          'No facility ID provided. Please ensure the ict-facility-id header is set.',
        ),
      );

      const response = await request(app)
        .get(url)
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(IctError.notFound().statusCode);
      expect(response.body)
        .to.have.property('detail')
        .that.includes('No facility ID provided');
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigFacts);
    });

    it('should work with selected facility ID from header', async () => {
      // Mock context service to use selected facility
      const contextService = Container.get(ContextService);
      sinon
        .stub(contextService, 'selectedFacilityId')
        .get(() => 'selected-facility');
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'selected-facility-map',
          name: 'Selected Facility',
          dataset: 'test-dataset',
          facilityId: 'selected-facility-id',
          tenantId: 'test-tenant-id',
          default: false,
        },
      ]);

      const mockFacts: MetricConfigFact[] = [
        {
          factType: 'fault_event',
          totalConfigs: 3,
          enabledConfigs: 2,
          active: true,
        },
      ];

      metricConfigServiceStub.getMetricConfigFacts.resolves(mockFacts);

      const response = await request(app)
        .get(url)
        .set('ict-facility-id', 'selected-facility')
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockFacts);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigFacts);
    });
  });
});
