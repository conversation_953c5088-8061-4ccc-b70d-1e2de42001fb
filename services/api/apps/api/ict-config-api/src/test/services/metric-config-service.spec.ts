import sinon from 'sinon';
import {expect} from 'chai';
import {
  ContextService,
  WinstonLogger,
  ConfigStore,
  RedisClient,
} from 'ict-api-foundations';
import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';
import {MetricConfigService} from '../../services/metric-config-service.ts';
import {MetricConfigStore} from '../../stores/metric-config-store.ts';
import {MetricConfigSummaryFilters} from '../../defs/metric-config-filters.ts';
import type {MetricConfigDetail} from '../../defs/metric-config-detail.ts';
import {MetricConfigFact} from '../../defs/metric-config-facts.ts';

describe('MetricConfigService', () => {
  let service: MetricConfigService;
  let metricConfigStore: MetricConfigStore;
  let context: ContextService;
  let logger: WinstonLogger;
  let configStore: ConfigStore;
  let redisClient: sinon.SinonStubbedInstance<RedisClient>;

  beforeEach(() => {
    context = new ContextService();
    logger = new WinstonLogger(context);
    configStore = new ConfigStore(context);
    redisClient = sinon.createStubInstance(RedisClient);
    metricConfigStore = new MetricConfigStore(context, logger, redisClient);
    service = new MetricConfigService(
      configStore,
      logger,
      context,
      metricConfigStore,
    );

    // Mock facility maps with facilityId property
    sinon.stub(context, 'facilityMaps').get(() => [
      {
        id: 'test-facility-map',
        name: 'Test Facility',
        dataset: 'test-dataset',
        facilityId: 'test-facility-id',
        tenantId: 'test-tenant-id',
        default: true,
      },
    ]);

    // Mock store's getMetricConfigs method
    sinon.stub(metricConfigStore, 'getMetricConfigSummaries').resolves([]);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getMetricConfigSummaries', () => {
    it('should return both default and custom metric configurations', async () => {
      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: true};
      mockDefaultConfig.active = {default: true};
      mockDefaultConfig.views = ['facility'];

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';
      mockCustomConfig.views = ['facility'];

      const mockConfigs = [mockDefaultConfig, mockCustomConfig];

      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).resolves(
        mockConfigs,
      );
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      const result = await service.getMetricConfigSummaries(filters);

      expect(result).to.deep.equal(
        mockConfigs.map(config => ({
          id: config.id,
          metricName: config.metricConfigName,
          configType: config.configType,
          nodeName: config.nodeName,
          factType: config.factType,
          enabled:
            config instanceof DefaultMetricConfigurationEntity
              ? config.enabled
              : null,
          active:
            config instanceof CustomMetricConfigurationEntity
              ? config.active
              : config.active,
          isCustom: config instanceof CustomMetricConfigurationEntity,
          facilityId:
            config instanceof CustomMetricConfigurationEntity
              ? config.facilityId
              : 'default',
          views: config.views,
        })),
      );
    });

    it('should throw not found error when no configurations are found', async () => {
      // Mock store to return empty array
      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).resolves(
        [],
      );

      const filters: MetricConfigSummaryFilters = {
        metricName: 'nonexistent-metric',
        configType: 'node',
      };

      await expect(
        service.getMetricConfigSummaries(filters),
      ).to.be.rejectedWith(
        'No metric configurations found matching the criteria',
      );
    });

    it('should filter out default configs that have custom versions', async () => {
      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: true};
      mockDefaultConfig.active = {default: true};
      mockDefaultConfig.views = ['facility'];

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';
      mockCustomConfig.views = ['facility'];

      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).resolves([
        mockCustomConfig,
      ]);
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      const result = await service.getMetricConfigSummaries(filters);

      expect(result).to.deep.equal([
        {
          id: mockCustomConfig.id,
          metricName: mockCustomConfig.metricConfigName,
          configType: mockCustomConfig.configType,
          nodeName: mockCustomConfig.nodeName,
          factType: mockCustomConfig.factType,
          enabled: null,
          active: mockCustomConfig.active,
          isCustom: true,
          facilityId: mockCustomConfig.facilityId,
          views: mockCustomConfig.views,
        },
      ]);
    });

    it('should filter out disabled configs', async () => {
      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: false};
      mockDefaultConfig.active = {default: true};

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';

      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).resolves(
        [],
      );
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      await expect(
        service.getMetricConfigSummaries(filters),
      ).to.be.rejectedWith(
        'No metric configurations found matching the criteria',
      );
    });

    it('should handle store errors', async () => {
      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).rejects(
        new Error('Store error'),
      );
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      await expect(
        service.getMetricConfigSummaries(filters),
      ).to.be.rejectedWith('Store error');
    });
  });

  describe('updateOrCreateMetricConfig', () => {
    it('should create a new metric configuration when using selected facility', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const mockResult = {
        config: new CustomMetricConfigurationEntity(),
        isNew: true,
      };
      Object.assign(mockResult.config, {
        id: 'new-config-id',
        ...input,
      });

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon.stub(metricConfigStore, 'putMetricConfig').resolves(mockResult);
      sinon.stub(service, 'getMetricConfigDetail').resolves({
        default: undefined,
        custom: undefined,
      });

      const result = await service.updateOrCreateMetricConfig(input);

      expect(result).to.deep.equal(mockResult);
      expect(result.isNew).to.be.true;
      expect(result.config.id).to.equal('new-config-id');
    });

    it('should throw error when no facility ID is provided', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      sinon.stub(context, 'selectedFacilityId').get(() => undefined);
      sinon.stub(context, 'facilityMaps').get(() => []);

      await expect(
        service.updateOrCreateMetricConfig(input),
      ).to.be.rejectedWith(
        'No facility ID provided. Please ensure the ict-facility-id header is set.',
      );
    });

    it('should throw error when no facility map is configured', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon.stub(context, 'facilityMaps').get(() => undefined);

      await expect(
        service.updateOrCreateMetricConfig(input),
      ).to.be.rejectedWith(
        'No facility map found for selected facility test-facility-map',
      );
    });

    it('should update an existing metric configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'existing-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Existing Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const mockResult = {
        config: new CustomMetricConfigurationEntity(),
        isNew: false,
      };
      Object.assign(mockResult.config, {
        id: 'existing-config-id',
        ...input,
      });

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon.stub(metricConfigStore, 'putMetricConfig').resolves(mockResult);
      sinon.stub(service, 'getMetricConfigDetail').resolves({
        default: undefined,
        custom: undefined,
      });

      const result = await service.updateOrCreateMetricConfig(input);

      expect(result).to.deep.equal(mockResult);
      expect(result.isNew).to.be.false;
      expect(result.config.id).to.equal('existing-config-id');
    });

    it('should handle store errors', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon
        .stub(metricConfigStore, 'putMetricConfig')
        .rejects(new Error('Database connection failed'));
      sinon.stub(service, 'getMetricConfigDetail').resolves({
        default: undefined,
        custom: undefined,
      });

      await expect(
        service.updateOrCreateMetricConfig(input),
      ).to.be.rejectedWith('Database connection failed');
    });

    describe('key deletion functionality', () => {
      const baseInput: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const mockResult = {
        config: new CustomMetricConfigurationEntity(),
        isNew: false,
      };

      beforeEach(() => {
        Object.assign(mockResult.config, {
          id: 'updated-config-id',
          ...baseInput,
        });
        sinon
          .stub(context, 'selectedFacilityId')
          .get(() => 'test-facility-map');
        sinon.stub(metricConfigStore, 'putMetricConfig').resolves(mockResult);
      });

      it('should delete keys when updating an enabled custom config', async () => {
        const oldCustomConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        // Mock the service's getMetricConfigDetail method to return the old config
        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: undefined,
          custom: oldCustomConfig,
        });

        const deleteKeysStub = sinon
          .stub(metricConfigStore, 'deleteMetricConfigKeys')
          .resolves(5);

        const result = await service.updateOrCreateMetricConfig(baseInput);

        expect(deleteKeysStub.calledOnce).to.be.true;
        expect(
          deleteKeysStub.calledWith(
            oldCustomConfig,
            'test-tenant-id',
            'test-facility-id',
          ),
        ).to.be.true;
        expect(result).to.deep.equal(mockResult);
      });

      it('should delete keys when updating an enabled default config', async () => {
        const oldDefaultConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: false,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        // Mock the service's getMetricConfigDetail method to return the old config
        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: oldDefaultConfig,
          custom: undefined,
        });

        const deleteKeysStub = sinon
          .stub(metricConfigStore, 'deleteMetricConfigKeys')
          .resolves(3);

        const result = await service.updateOrCreateMetricConfig(baseInput);

        expect(deleteKeysStub.calledOnce).to.be.true;
        expect(
          deleteKeysStub.calledWith(
            oldDefaultConfig,
            'test-tenant-id',
            'test-facility-id',
          ),
        ).to.be.true;
        expect(result).to.deep.equal(mockResult);
      });

      it('should NOT delete keys when old custom config is disabled', async () => {
        const oldCustomConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: false,
          active: true,
          isCustom: true,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: undefined,
          custom: oldCustomConfig,
        });

        const deleteKeysStub = sinon
          .stub(metricConfigStore, 'deleteMetricConfigKeys')
          .resolves(0);

        const result = await service.updateOrCreateMetricConfig(baseInput);

        expect(deleteKeysStub.called).to.be.false;
        expect(result).to.deep.equal(mockResult);
      });

      it('should NOT delete keys when old default config is disabled', async () => {
        const oldDefaultConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: false,
          active: true,
          isCustom: false,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: oldDefaultConfig,
          custom: undefined,
        });

        const deleteKeysStub = sinon
          .stub(metricConfigStore, 'deleteMetricConfigKeys')
          .resolves(0);

        const result = await service.updateOrCreateMetricConfig(baseInput);

        expect(deleteKeysStub.called).to.be.false;
        expect(result).to.deep.equal(mockResult);
      });

      it('should NOT delete keys when no old config exists', async () => {
        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: undefined,
          custom: undefined,
        });

        const deleteKeysStub = sinon
          .stub(metricConfigStore, 'deleteMetricConfigKeys')
          .resolves(0);

        const result = await service.updateOrCreateMetricConfig(baseInput);

        expect(deleteKeysStub.called).to.be.false;
        expect(result).to.deep.equal(mockResult);
      });

      it('should prioritize custom config over default config for deletion', async () => {
        const oldCustomConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        const oldDefaultConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: false,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: oldDefaultConfig,
          custom: oldCustomConfig,
        });

        const deleteKeysStub = sinon
          .stub(metricConfigStore, 'deleteMetricConfigKeys')
          .resolves(2);

        const result = await service.updateOrCreateMetricConfig(baseInput);

        // Should only call delete once for custom config, not default
        expect(deleteKeysStub.calledOnce).to.be.true;
        expect(
          deleteKeysStub.calledWith(
            sinon.match.any,
            'test-tenant-id',
            'test-facility-id',
          ),
        ).to.be.true;
        expect(result).to.deep.equal(mockResult);
      });

      it('should handle different config types for deletion', async () => {
        const edgeInput: MetricConfigDetail = {
          ...baseInput,
          configType: 'inbound-edge',
          inboundArea: 'receiving',
          huId: 'handling_unit_code',
          redisOperation: 'event_set',
          metricUnits: 'units/hr',
          inboundParentNodes: ['warehouse'],
        };

        const oldCustomConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'inbound-edge',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          inboundArea: 'receiving',
          huId: 'handling_unit_code',
          redisOperation: 'event_set',
          graphOperation: 'area_edge',
          metricUnits: 'units/hr',
          inboundParentNodes: ['warehouse'],
        };

        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: undefined,
          custom: oldCustomConfig,
        });

        const deleteKeysStub = sinon
          .stub(metricConfigStore, 'deleteMetricConfigKeys')
          .resolves(1);

        mockResult.config = new CustomMetricConfigurationEntity();
        Object.assign(mockResult.config, {
          id: 'updated-edge-config-id',
          ...edgeInput,
        });

        const result = await service.updateOrCreateMetricConfig(edgeInput);

        expect(deleteKeysStub.calledOnce).to.be.true;
        expect(
          deleteKeysStub.calledWith(
            sinon.match.any,
            'test-tenant-id',
            'test-facility-id',
          ),
        ).to.be.true;
        expect(result).to.deep.equal(mockResult);
      });

      it('should log the number of deleted keys', async () => {
        const oldCustomConfig: MetricConfigDetail = {
          metricConfigName: 'test-metric',
          configType: 'node',
          views: ['facility'],
          matchConditions: {eventType: 'test'},
          factType: 'test-fact',
          sourceSystem: 'diq',
          displayName: 'Test Metric',
          enabled: true,
          active: true,
          isCustom: true,
          nodeName: 'test-node',
          metricType: 'testType',
          timeWindow: '60m_set',
          aggregation: 'sum',
          redisOperation: 'event_set',
          graphOperation: 'area_node',
        };

        sinon.stub(service, 'getMetricConfigDetail').resolves({
          default: undefined,
          custom: oldCustomConfig,
        });

        sinon.stub(metricConfigStore, 'deleteMetricConfigKeys').resolves(7);

        const loggerInfoStub = sinon.stub(logger, 'info');

        await service.updateOrCreateMetricConfig(baseInput);

        expect(
          loggerInfoStub.calledWith(
            'Deleted 7 keys associated with the previous custom metric config for test-metric',
          ),
        ).to.be.true;
      });
    });
  });

  describe('getMetricConfigDetail', () => {
    beforeEach(() => {
      // Mock facility maps with facilityId
      sinon.stub(context, 'facilityMaps').get(() => [
        {
          id: 'test-facility-map',
          name: 'Test Facility',
          dataset: 'test-dataset',
          facilityId: 'test-facility-id',
          tenantId: 'test-tenant-id',
          default: true,
        },
      ]);
    });

    it('should return both default and custom configurations', async () => {
      const metricName = 'test-metric';

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      Object.assign(mockDefaultConfig, {
        id: 'default-1',
        metricConfigName: metricName,
        configType: 'node',
        factType: 'test-fact',
        enabled: {default: true},
        active: {default: true},
      });

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      Object.assign(mockCustomConfig, {
        id: 'custom-1',
        metricConfigName: metricName,
        configType: 'node',
        factType: 'test-fact',
        facilityId: 'test-facility-id',
        enabled: true,
        active: true,
      });

      sinon.stub(metricConfigStore, 'getMetricConfigDetail').resolves({
        default: mockDefaultConfig,
        custom: mockCustomConfig,
      });

      const result = await service.getMetricConfigDetail(metricName);

      expect(result.default).to.exist;
      expect(result.custom).to.exist;
      expect(result.default?.metricConfigName).to.equal(metricName);
      expect(result.custom?.metricConfigName).to.equal(metricName);
    });

    it('should return only default configuration when configType is "default"', async () => {
      const metricName = 'test-metric';

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      Object.assign(mockDefaultConfig, {
        id: 'default-1',
        metricConfigName: metricName,
        configType: 'node',
        factType: 'test-fact',
        enabled: {default: true},
        active: {default: true},
      });

      sinon.stub(metricConfigStore, 'getMetricConfigDetail').resolves({
        default: mockDefaultConfig,
        custom: undefined,
      });

      const result = await service.getMetricConfigDetail(metricName, 'default');

      expect(result.default).to.exist;
      expect(result.custom).to.be.undefined;
      expect(result.default?.metricConfigName).to.equal(metricName);
    });

    it('should return only custom configuration when configType is "custom"', async () => {
      const metricName = 'test-metric';

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      Object.assign(mockCustomConfig, {
        id: 'custom-1',
        metricConfigName: metricName,
        configType: 'node',
        factType: 'test-fact',
        facilityId: 'test-facility-id',
        enabled: true,
        active: true,
      });

      sinon.stub(metricConfigStore, 'getMetricConfigDetail').resolves({
        default: undefined,
        custom: mockCustomConfig,
      });

      const result = await service.getMetricConfigDetail(metricName, 'custom');

      expect(result.default).to.be.undefined;
      expect(result.custom).to.exist;
      expect(result.custom?.metricConfigName).to.equal(metricName);
    });

    it('should throw error when no facility map is found', async () => {
      const metricName = 'test-metric';

      sinon
        .stub(context, 'selectedFacilityId')
        .get(() => 'nonexistent-facility');

      await expect(
        service.getMetricConfigDetail(metricName),
      ).to.be.rejectedWith(
        'No facility map found for selected facility nonexistent-facility',
      );
    });

    it('should throw error when facility map has no facilityId', async () => {
      const metricName = 'test-metric';

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon.stub(context, 'facilityMaps').get(() => [
        {
          id: 'test-facility-map',
          name: 'Test Facility',
          dataset: 'test-dataset',
          facilityId: undefined, // No facilityId
          tenantId: 'test-tenant-id',
          default: true,
        },
      ]);

      await expect(
        service.getMetricConfigDetail(metricName),
      ).to.be.rejectedWith(
        'No facility ID provided. Please ensure the ict-facility-id header is set and a facility map is configured.',
      );
    });

    it('should handle store errors', async () => {
      const metricName = 'test-metric';

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility-map');
      sinon
        .stub(metricConfigStore, 'getMetricConfigDetail')
        .rejects(new Error('Database connection failed'));

      await expect(
        service.getMetricConfigDetail(metricName),
      ).to.be.rejectedWith('Database connection failed');
    });
  });

  describe('getMetricConfigFacts', () => {
    it('should return metric configuration facts for the selected facility', async () => {
      const mockFacts: MetricConfigFact[] = [
        {
          factType: 'inventory',
          totalConfigs: 5,
          enabledConfigs: 3,
          active: true,
        },
        {
          factType: 'shipping',
          totalConfigs: 2,
          enabledConfigs: 1,
          active: false,
        },
      ];

      const getMetricConfigFactsStub = sinon
        .stub(metricConfigStore, 'getMetricConfigFacts')
        .resolves(mockFacts);

      const result = await service.getMetricConfigFacts();

      expect(result).to.deep.equal(mockFacts);
      sinon.assert.calledOnce(getMetricConfigFactsStub);
      sinon.assert.calledWith(getMetricConfigFactsStub, 'test-facility-id');
    });

    it('should return empty array when no facts are found', async () => {
      const mockFacts: MetricConfigFact[] = [];

      const getMetricConfigFactsStub = sinon
        .stub(metricConfigStore, 'getMetricConfigFacts')
        .resolves(mockFacts);

      const result = await service.getMetricConfigFacts();

      expect(result).to.be.an('array').that.is.empty;
      sinon.assert.calledOnce(getMetricConfigFactsStub);
    });

    it('should throw error when no facility ID is provided', async () => {
      sinon.stub(context, 'selectedFacilityId').get(() => undefined);
      sinon.stub(context, 'facilityMaps').get(() => []);

      await expect(service.getMetricConfigFacts()).to.be.rejectedWith(
        'No facility ID provided. Please ensure the ict-facility-id header is set.',
      );
    });

    it('should throw error when no facility map is found', async () => {
      sinon
        .stub(context, 'selectedFacilityId')
        .get(() => 'nonexistent-facility');

      await expect(service.getMetricConfigFacts()).to.be.rejectedWith(
        'No facility map found for selected facility nonexistent-facility',
      );
    });

    it('should handle database errors gracefully', async () => {
      sinon
        .stub(metricConfigStore, 'getMetricConfigFacts')
        .rejects(new Error('Database connection failed'));

      await expect(service.getMetricConfigFacts()).to.be.rejectedWith(
        'Database connection failed',
      );
    });

    it('should use facility ID from facility maps when selectedFacilityId is not set', async () => {
      const mockFacts: MetricConfigFact[] = [
        {
          factType: 'fault_event',
          totalConfigs: 3,
          enabledConfigs: 2,
          active: true,
        },
      ];

      // Mock context to not have selectedFacilityId but have default facility
      sinon.stub(context, 'selectedFacilityId').get(() => undefined);

      const getMetricConfigFactsStub = sinon
        .stub(metricConfigStore, 'getMetricConfigFacts')
        .resolves(mockFacts);

      const result = await service.getMetricConfigFacts();

      expect(result).to.deep.equal(mockFacts);
      sinon.assert.calledOnce(getMetricConfigFactsStub);
      sinon.assert.calledWith(
        getMetricConfigFactsStub,
        'test-facility-id', // Should use facilityId from facility maps
      );
    });
  });
});
