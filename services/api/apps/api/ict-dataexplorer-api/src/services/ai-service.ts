import {
  Container,
  ContextService,
  DiService,
  Environment,
  EnvironmentService,
  GCPSecretManager,
  GCPSecretManagerQuery,
  IctError,
  RedisClient,
  WinstonLogger,
} from 'ict-api-foundations';
import axios from 'axios';
import {GoogleAuth} from 'google-auth-library';
import {randomUUID} from 'crypto';
import {DataExplorerSearchAIRequest} from '../defs/dataexplorer-search-ai-request';
import {
  DataExplorerGoldQuestionsResponse,
  AIMLResponse,
  DataExplorerSearchAIResponse,
  DataExplorerAgentSearchResponse,
} from '../defs/dataexplorer-search-ai-response';

/** TTL in seconds for the recommendations key retrieval and cache. */
const REDIS_CACHE_TTL_RECOMMENDED_QUESTIONS = 60 * 60 * 24; // 24 hours

/** TTL in seconds for the gold questions key retrieval and cache. */
const REDIS_CACHE_TTL_GOLD_QUESTIONS = 60 * 60 * 24; // 24 hours

@DiService()
export class AIService {
  private logger: WinstonLogger;
  private context: ContextService;
  private redisClient: RedisClient;
  private envService: EnvironmentService;

  constructor(context: ContextService, redisClient: RedisClient) {
    this.logger = Container.get(WinstonLogger);
    this.envService = Container.get(EnvironmentService);
    this.logger.info('AIService initialized');
    this.context = context;
    this.redisClient = redisClient;
  }

  public async getRecommendations(): Promise<DataExplorerGoldQuestionsResponse> {
    const tenantId = this.context.databaseId;
    const cacheKey = `${tenantId}:data_explorer_recommended_questions`;
    try {
      const cachedData = await this.redisClient.get(cacheKey);
      if (cachedData) {
        const ttl = await this.redisClient.ttl(cacheKey);
        this.logger.info('Data Explorer recommended questions cache hit', {
          cacheKey,
          ttl,
          cachedData,
        });
        const cachedRecommendations = JSON.parse(cachedData);
        return cachedRecommendations;
      }
    } catch (e) {
      this.logger.error(
        'Error getting cached Data Explorer recommended questions:',
        e instanceof Error ? e : {},
      );
    }

    let dataExplorerUrlBase: string;

    const nodeEnv = process.env.ENVIRONMENT;

    if (!nodeEnv || nodeEnv !== Environment.local) {
      // use secret manager here for non-dev environments
      const secretManager = Container.get(GCPSecretManager);
      if (!secretManager) {
        throw new Error(
          'A secret manager is required for accessing the Data Explorer url.',
        );
      }
      const query = AIService.generateAIMLInvocationUrl();
      dataExplorerUrlBase = await secretManager.get(query);
    } else {
      dataExplorerUrlBase = process.env.AIML_INVOCATION_URL || '';
    }
    const token = await this.fetchIdToken(dataExplorerUrlBase);
    const dataExplorerUrl = process.env.AIML_DATA_EXPLORER_URL;
    this.logger.info('Data Explorer URL:', {
      dataExplorerUrlBase,
      dataExplorerUrl,
    });
    const dateExplorerRecommendationsUrl = `${dataExplorerUrl}/gold_questions`;

    const response = await axios.get<DataExplorerGoldQuestionsResponse>(
      dateExplorerRecommendationsUrl,
      {
        headers: {
          'Agent-Name': 'control-tower',
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        // timeout: 120000,
      },
    );

    if (response.status !== 200) {
      this.logger.info('Bad response from Data Explorer', response);
      return [];
    }

    this.redisClient.setEx(
      cacheKey,
      REDIS_CACHE_TTL_RECOMMENDED_QUESTIONS,
      JSON.stringify(response.data),
    );

    return response.data;
  }

  public async getGoldQuestions(): Promise<DataExplorerGoldQuestionsResponse> {
    const tenantId = this.context.databaseId;
    const cacheKey = `${tenantId}:dematic_chat_gold_questions`;
    try {
      const cachedData = await this.redisClient.get(cacheKey);
      if (cachedData) {
        const ttl = await this.redisClient.ttl(cacheKey);
        this.logger.info('Dematic Chat gold questions cache hit', {
          cacheKey,
          ttl,
          cachedData,
        });
        const cachedGoldQuestions = JSON.parse(cachedData);
        return cachedGoldQuestions;
      }
    } catch (e) {
      this.logger.error(
        'Error getting cached Dematic Chat gold questions:',
        e instanceof Error ? e : {},
      );
    }

    const goldQuestionsAudienceUrl = process.env.GOLD_QUESTIONS_AUDIENCE_URL;
    const goldQuestionsUrl = process.env.AGENT_SEARCH_URL;

    if (!goldQuestionsAudienceUrl || !goldQuestionsUrl) {
      throw new Error(
        'GOLD_QUESTIONS_AUDIENCE_URL and AGENT_SEARCH_URL environment variables are required.',
      );
    }

    const token = await this.fetchIdToken(goldQuestionsAudienceUrl);
    this.logger.info('Gold Questions URL:', {
      goldQuestionsAudienceUrl,
      goldQuestionsUrl,
    });
    const dematicChatGoldQuestionsUrl = `${goldQuestionsUrl}/ragengine/get_all_questions`;

    const response = await axios.get<DataExplorerGoldQuestionsResponse>(
      dematicChatGoldQuestionsUrl,
      {
        headers: {
          'Agent-Name': 'control-tower',
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      },
    );

    if (response.status !== 200) {
      this.logger.info(
        'Bad response from Data Explorer for gold questions',
        response,
      );
      return [];
    }

    try {
      await this.redisClient.setEx(
        cacheKey,
        REDIS_CACHE_TTL_GOLD_QUESTIONS,
        JSON.stringify(response.data),
      );
    } catch (cacheError) {
      this.logger.warn('Failed to cache gold questions response', {
        error: cacheError instanceof Error ? cacheError.message : cacheError,
      });
    }

    return response.data;
  }

  public async search(
    request: DataExplorerSearchAIRequest,
  ): Promise<DataExplorerSearchAIResponse> {
    let aimlUrlBase: string;

    const nodeEnv = process.env.ENVIRONMENT;

    if (!nodeEnv || nodeEnv !== Environment.local) {
      // use secret manager here for non-dev environments
      const secretManager = Container.get(GCPSecretManager);
      if (!secretManager) {
        throw new Error(
          'A secret manager is required for accessing the AIML Invocation url.',
        );
      }
      const query = AIService.generateAIMLInvocationUrl();
      aimlUrlBase = await secretManager.get(query);
    } else {
      aimlUrlBase = process.env.AIML_INVOCATION_URL || '';
    }
    const dataExplorerUrl = process.env.AIML_DATA_EXPLORER_URL;
    this.logger.info('Data Explorer URL:', {
      aimlUrlBase,
      dataExplorerUrl,
    });
    const token = await this.fetchIdToken(aimlUrlBase);
    const aimlUrl = `${dataExplorerUrl}/natural_response`;
    this.logger.info('AIML Request:', {
      aimlUrl,
      user_question: request.user_question,
      user_database: request.user_database,
    });

    let data;
    try {
      data = await axios.post<AIMLResponse>(
        aimlUrl,
        {
          user_question: request.user_question,
          user_database: request.user_database,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
            'Agent-Name': 'control-tower',
          },
          // timeout: 120000,
        },
      );
    } catch (error) {
      // Log the raw error details for debugging
      this.logger.error('AIML Request failed - Raw Error Details:', {
        error,
        url: aimlUrl,
        requestData: {
          user_question: request.user_question,
          user_database: request.user_database,
        },
      });

      // Re-throw the original error to preserve all details
      throw IctError.internalServerError('Data Explorer Request Failed');
    }
    // Only log specific response properties to avoid circular reference issues
    this.logger.info('Raw AIML Response', {
      responseCode: data.data.ResponseCode,
      knownDB: data.data.KnownDB,
      hasEchartCode: !!data.data.echart_code,
      hasSqlOutput: !!data.data.sql_query_output,
      hasSummary: !!data.data.summary_response,
    });

    // Parse eChart Options if present
    let echart_code = null;
    let sql_query_output;
    if (data.data.echart_code) {
      echart_code = this.parseLLMEChartsCode(data.data.echart_code as string);
    }
    if (data.data.sql_query_output) {
      sql_query_output = this.parseSQLQueryOutput(
        data.data.sql_query_output as string,
      );
    }

    return {
      error: data.data.Error,
      known_DB: data.data.KnownDB,
      response_code: data.data.ResponseCode,
      echart_code: data.data.echart_code,
      parsed_echart_code: echart_code,
      generated_sql_query: data.data.generated_sql_query,
      question: data.data.question,
      sql_query_output: sql_query_output || [],
      summary_response: data.data.summary_response,
      debug_data: data.data,
    };
  }

  public async getAgentSearch(
    request: DataExplorerSearchAIRequest,
  ): Promise<DataExplorerAgentSearchResponse> {
    const messages: string[] = [];

    const agentSearchAudienceUrl = process.env.AGENT_SEARCH_AUDIENCE_URL;
    const agentSearchUrl = process.env.AGENT_SEARCH_URL;
    const appName = 'data_explorer_agent';

    // Add detailed logging for debugging
    this.logger.info('Agent Search Environment Check:', {
      agentSearchAudienceUrl: agentSearchAudienceUrl ? 'SET' : 'NOT SET',
      agentSearchUrl: agentSearchUrl ? 'SET' : 'NOT SET',
      environment: process.env.ENVIRONMENT,
    });

    if (!agentSearchAudienceUrl || !agentSearchUrl) {
      throw new Error(
        'AGENT_SEARCH_AUDIENCE_URL and AGENT_SEARCH_URL environment variables are required.',
      );
    }

    const userEmail = this.context.userEmail;
    const sessionId = request.userSessionId || randomUUID();

    // set a LOCAL_ID_TOKEN token in .env.local for local development and ensure you are connected to Pritunl VPN
    // generate token by running gcloud auth print-identity-token --impersonate-service-account=SERVICE_ACCOUNT_EMAIL --audiences=AUDIENCE
    const idToken = await this.fetchIdToken(agentSearchAudienceUrl);

    if (!request.userSessionId) {
      const endpointPath = `${agentSearchUrl}/dematic-chat/apps/${appName}/users/${userEmail}/sessions/${sessionId}`;
      const requestBody: {state: {}; dataset_id?: string} = {
        state: {},
      };

      if (request.user_database) {
        requestBody.dataset_id = request.user_database;
      }

      this.logger.info('Sending POST request to new agent session endpoint:', {
        url: endpointPath,
        body: requestBody,
      });

      const postResponse = await axios.post<unknown>(
        endpointPath,
        requestBody,
        {
          headers: {
            Authorization: `Bearer ${idToken}`,
            'Content-Type': 'application/json',
          },
        },
      );

      this.logger.info('New agent session endpoint POST response:', {
        status: postResponse.status,
        data: postResponse.data,
      });

      if (!(postResponse.status >= 200 && postResponse.status < 300)) {
        this.logger.error(
          'POST request to new agent session endpoint failed.',
          {
            status: postResponse.status,
            data: postResponse.data,
          },
        );
        throw new Error(
          `Failed to interact with agent session endpoint. Status: ${postResponse.status}`,
        );
      }
    }

    this.logger.info('Using session ID for streaming:', {
      sessionId,
    });

    // --- Streaming Query ---
    const streamUrl = `${agentSearchUrl}/dematic-chat/run_sse`;
    this.logger.info('AIML Stream Query URL:', {streamUrl});

    const streamRequestBody = {
      app_name: appName,
      user_id: userEmail,
      session_id: sessionId,
      new_message: {
        role: 'user',
        parts: [
          {
            text: request.user_question,
          },
        ],
      },
      streaming: true,
    };

    this.logger.info('Initiating stream query:', {
      url: streamUrl,
      body: streamRequestBody,
    });

    return new Promise<DataExplorerAgentSearchResponse>(resolve => {
      axios
        .post(streamUrl, streamRequestBody, {
          headers: {
            Authorization: `Bearer ${idToken}`,
            'Content-Type': 'application/json; charset=utf-8',
            Accept: 'text/event-stream',
          },
          responseType: 'stream',
          timeout: 120000,
        })
        .then(response => {
          if (response.status !== 200) {
            this.logger.error(
              `SSE stream request failed with status ${response.status}`,
              {
                statusText: response.statusText,
                headers: response.headers,
                sessionId,
              },
            );
            resolve({
              sessionId,
              status: 'error',
              messages: [`Request failed with status ${response.status}`],
            });
            return;
          }

          this.logger.info('SSE stream request successful:', {
            status: response.status,
            contentType: response.headers['content-type'],
            sessionId,
          });

          this.logger.info('SSE connection opened via axios stream.');
          const stream = response.data;
          let buffer = '';

          stream.on('data', (chunk: Buffer) => {
            const chunkStr = chunk.toString('utf-8');
            this.logger.debug('Raw SSE chunk received:', {
              chunk: chunkStr,
              chunkLength: chunkStr.length,
              bufferLengthBefore: buffer.length,
            });
            buffer += chunkStr;

            let newlineIndex = buffer.indexOf('\n');
            while (newlineIndex >= 0) {
              const line = buffer.substring(0, newlineIndex).trim();
              buffer = buffer.substring(newlineIndex + 1);

              if (line) {
                try {
                  let jsonString = line;
                  if (line.startsWith('data: ')) {
                    jsonString = line.substring(5).trim();
                  }

                  if (jsonString) {
                    const parsedJson = JSON.parse(jsonString);
                    this.logger.debug('Parsed JSON line:', {parsedJson});

                    const textContent = parsedJson?.content?.parts?.[0]?.text;
                    if (textContent) {
                      this.logger.info('Extracted text content:', {
                        textContent,
                      });
                      messages.push(textContent);
                    } else if (parsedJson?.author) {
                      this.logger.info(
                        `Received message from author: ${parsedJson.author}`,
                      );
                    } else {
                      // Log when no text content is found to debug the structure
                      this.logger.warn(
                        'No text content found in parsed JSON:',
                        {
                          jsonString,
                          parsedJson,
                          hasContent: !!parsedJson?.content,
                          hasParts: !!parsedJson?.content?.parts,
                          partsLength: parsedJson?.content?.parts?.length || 0,
                          firstPartKeys: parsedJson?.content?.parts?.[0]
                            ? Object.keys(parsedJson.content.parts[0])
                            : [],
                        },
                      );
                    }
                  }
                } catch (parseError) {
                  this.logger.warn('Failed to parse line as JSON:', {
                    originalLine: line,
                    processedJsonString: line.startsWith('data: ')
                      ? line.substring(5).trim()
                      : line,
                    error:
                      parseError instanceof Error
                        ? parseError.message
                        : parseError,
                  });
                }
              }
              newlineIndex = buffer.indexOf('\n');
            }
          });

          stream.on('end', () => {
            this.logger.info('SSE stream ended by server.');
            if (buffer.trim()) {
              this.logger.warn(
                'Processing remaining buffer content after stream end:',
                {buffer},
              );
              try {
                let finalJsonString = buffer.trim();
                if (finalJsonString.startsWith('data: ')) {
                  finalJsonString = finalJsonString.substring(5).trim();
                }

                if (finalJsonString) {
                  const parsedJson = JSON.parse(finalJsonString);
                  this.logger.debug('Parsed final JSON buffer:', {parsedJson});
                  const textContent = parsedJson?.content?.parts?.[0]?.text;
                  if (textContent) {
                    this.logger.info('Extracted final text content:', {
                      textContent,
                    });
                    messages.push(textContent);
                  }
                }
              } catch (parseError) {
                this.logger.error(
                  'Failed to parse final buffer content as JSON:',
                  {
                    originalBuffer: buffer.trim(),
                    processedJsonString: buffer.trim().startsWith('data: ')
                      ? buffer.trim().substring(5).trim()
                      : buffer.trim(),
                    error:
                      parseError instanceof Error
                        ? parseError.message
                        : parseError,
                  },
                );
              }
            }

            this.logger.info('Resolving promise with collected messages.', {
              totalMessages: messages.length,
              messages: messages.slice(0, 3), // Log first 3 messages for debugging
              sessionId,
            });
            resolve({sessionId, status: 'completed', messages});
          });

          stream.on('error', (error: Error) => {
            this.logger.error('SSE stream error occurred:', {
              error: error.message,
              stack: error.stack,
              messagesCollectedSoFar: messages.length,
              sessionId,
            });
            resolve({sessionId, status: 'error', messages});
          });
        })
        .catch(error => {
          const axiosError = error as import('axios').AxiosError;
          this.logger.error('Error initiating SSE stream request:', {
            message: axiosError.message,
            code: axiosError.code,
            status: axiosError.response?.status,
          });

          if (axiosError.response?.status === 404) {
            this.logger.error('Received 404 status code initiating stream.');
            resolve({
              sessionId,
              status: 'error',
              messages: ['Non-200 status code (404)'],
            });
          } else {
            resolve({
              sessionId,
              status: 'error',
              messages: [axiosError.message || 'Failed to initiate stream'],
            });
          }
        });
    });
  }

  private async fetchIdToken(audience: string): Promise<string> {
    this.logger.info('In fetchIdToken');
    const auth = new GoogleAuth();

    const nodeEnv = process.env.ENVIRONMENT;

    if (nodeEnv && nodeEnv === Environment.local) {
      this.logger.info('In local token generation');
      const localToken = process.env.LOCAL_ID_TOKEN;
      if (!localToken) {
        this.logger.error('LOCAL_ID_TOKEN not set in environment');
        throw new Error(
          'LOCAL_ID_TOKEN environment variable is required for local development',
        );
      }
      this.logger.info('Using local development token', {audience});
      return localToken;
    }

    this.logger.info('Fetching ID token for audience', {audience});
    const client = await auth.getIdTokenClient(audience);
    this.logger.info('Client created');
    const token = await client.idTokenProvider.fetchIdToken(audience);
    this.logger.info('Token fetched');
    if (!token) {
      throw new Error('Failed to obtain ID token');
    }

    return token;
  }

  private static generateAIMLInvocationUrl(): GCPSecretManagerQuery {
    const secretName = process.env.AIML_INVOCATION_URL || '';
    const query: GCPSecretManagerQuery = {
      name: secretName,
    };

    return query;
  }

  /*
  Example payload from AIML Engine
  ```{\n  \"title\": {\n    \"text\": \"Number of Operators Who Began Working in 2024\"\n  },\n  \"series\": [{\n    \"type\": \"gauge\",\n    \"min\": 0,\n    \"max\": 100, \n    \"splitNumber\": 10,\n    \"radius\": '90%',\n    \"center\": ['50%', '50%'],\n    \"axisLine\": {\n      \"lineStyle\": {\n        \"width\": 10,\n        \"color\": [\n          [0.8, '#2F4554'],\n          [1, '#E7E5E5']\n        ]\n      }\n    },\n    \"pointer\": {\n      \"itemStyle\": {\n        \"color\": 'auto'\n      }\n    },\n    \"axisTick\": {\n      \"distance\": -35,\n      \"length\": 8,\n      \"lineStyle\": {\n        \"color\": '#fff',\n        \"width\": 1\n      }\n    },\n    \"splitLine\": {\n      \"distance\": -35,\n      \"length\": 14,\n      \"lineStyle\": {\n        \"color\": '#fff',\n        \"width\": 2\n      }\n    },\n    \"axisLabel\": {\n      \"color\": 'inherit',\n      \"distance\": -20,\n      \"fontSize\": 12\n    },\n    \"detail\": {\n      \"valueAnimation\": true,\n      \"formatter\": '{value}',\n      \"color\": 'inherit',\n      \"fontSize\": 20\n    },\n    \"data\": [{\n      \"value\": 65,\n      \"name\": 'Operators'\n    }]\n  }]\n}```
  */
  private parseLLMEChartsCode(eChartCode: string): unknown | null {
    try {
      // Clean up the input string
      let processedCode = eChartCode.trim();

      // Remove markdown code block markers if present
      processedCode = processedCode.replace(/^```json\s*|\s*```$/gm, '');

      // Remove "option = " prefix and trailing semicolon if present
      processedCode = processedCode
        .replace(/^option\s*=\s*/, '')
        .replace(/;$/, '');

      processedCode = processedCode
        // Handle properties with single quotes
        .replace(/'([^']+)'/g, (match, p1) => {
          // Don't double escape already escaped quotes
          if (p1.includes('"')) return match;
          return `"${p1}"`;
        })
        // Handle unquoted property names, including those with hyphens and numbers
        .replace(/([a-zA-Z][a-zA-Z0-9_-]*)\s*:/g, '"$1":')
        // Handle any remaining unquoted property names
        .replace(/([^"\s{,]+):/g, '"$1":');

      // First try parsing as direct JSON
      const parsed = JSON.parse(processedCode);

      // If we have an "option" wrapper, return its contents
      if (parsed.option) {
        return parsed.option;
      }

      // Otherwise return the parsed object itself
      return parsed;
    } catch (e) {
      const error = e as Error;
      this.logger.error('Failed to parse eCharts Code', error);
      return null;
    }
  }

  private parseSQLQueryOutput(sqlQueryOutput: string): unknown[] | undefined {
    try {
      // Try parsing the string as JSON array
      const parsedOutput = JSON.parse(sqlQueryOutput);

      // Ensure the parsed output is an array
      if (!Array.isArray(parsedOutput)) {
        this.logger.error('SQL query output is not an array');
        return [];
      }

      return parsedOutput;
    } catch (e) {
      const error = e as Error;
      this.logger.error('Failed to parse SQL query output', error);
      return [];
    }
  }
}
