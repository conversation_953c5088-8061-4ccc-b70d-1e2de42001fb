import {createClient, RedisClientType} from 'redis';
import {Environment} from '@ict/sdk-foundations/types/index.ts';
import {DiService} from '../di/type-di.ts';
import {WinstonLogger} from '../log/winston-logger.ts';
import {EnvironmentService} from '../services/environment-service.ts';

@DiService()
export class RedisClient {
  private redisClient: RedisClientType;

  constructor(
    private logger: WinstonLogger,
    private envService: EnvironmentService,
  ) {
    const urlPath = `redis://default:${this.envService.redis.authString}@${this.envService.redis.host}:${this.envService.redis.port}`;

    this.redisClient = createClient({
      url: urlPath,
      socket: {
        // This Reconnect Strategy helps speed up the service startup-times
        // if redis is down
        reconnectStrategy: (retries: number): number | Error => {
          if (retries > 5) {
            return new Error('Too many retries.');
          }
          return retries;
        },
      },
    });

    this.redisClient.on('error', err =>
      this.logger.error('Redis Client Error', err),
    );

    this.connect();
  }

  public get(cacheKey: string) {
    return this.redisClient.get(cacheKey);
  }

  public ttl(cacheKey: string) {
    return this.redisClient.ttl(cacheKey);
  }

  public set(cacheKey: string, value: string) {
    return this.redisClient.set(cacheKey, value);
  }

  public setEx(cacheKey: string, ttl: number, value: string) {
    return this.redisClient.setEx(cacheKey, ttl, value);
  }

  public isReady() {
    return this.redisClient.isReady;
  }

  public zCount(key: string, min: number, max: number) {
    return this.redisClient.zCount(key, Math.floor(min), Math.floor(max));
  }

  public zRange(key: string, min: number, max: number) {
    return this.redisClient.zRangeByScore(
      key,
      Math.floor(min),
      Math.floor(max),
    );
  }

  public async keys(pattern: string): Promise<string[]> {
    return this.redisClient.keys(pattern);
  }

  // Removes key or keys based on the key name (not pattern)
  public async unlink(keys: string | string[]): Promise<number> {
    if (Array.isArray(keys) && keys.length > 0) {
      this.logger.debug('Deleting Redis keys', {keys});
      return this.redisClient.unlink(keys);
    }
    if (typeof keys === 'string') {
      this.logger.debug('Deleting Redis keys', {keys});
      return this.redisClient.unlink([keys]);
    }
    return 0;
  }

  // Removes key or keys based on a pattern
  public async clearKeys(pattern: string) {
    const keys = await this.redisClient.keys(pattern);
    if (keys.length) {
      this.logger.info('Deleting Redis keys', {keys});
      await this.redisClient.del(keys);
    }
  }

  public multi() {
    return this.redisClient.multi();
  }

  async connect() {
    // Normally in local development we do not want request caching enabled so we can
    // view our code changes in real-time. Return immediately in localhost unless we have
    // turned on the override.
    if (
      this.envService.app.env === Environment.local &&
      !this.envService.redis.localRequestCacheOverride
    ) {
      return;
    }

    try {
      await this.redisClient.connect();
      this.logger.debug('Redis was not connected, but we tried again.');
      this.logger.debug(
        this.redisClient.isReady ? 'Redis is ready now!' : 'Still not ready',
      );
    } catch (e) {
      const error = e as Error;
      this.logger.error('Redis connection error', error);
    }
  }
}
